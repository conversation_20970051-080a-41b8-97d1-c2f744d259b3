import os
import datetime
import psycopg2
import pandas as pd
import nltk
from sentence_transformers import SentenceTransformer

# Download sentence tokenizer once
try:
    nltk.download('punkt_tab')
    nltk.download('punkt')  # Keep both for compatibility
except Exception as e:
    print(f"Warning: Could not download NLTK data: {e}")
from nltk.tokenize import sent_tokenize

# --- CONFIGURATION ---
XLSX_FILE_PATH = './pages2.xlsx'
LAST_INDEXED_FILE_PATH = './last_chunk_indexed.txt'

DEST_CONN_DETAILS = {
    "user": "ai_chat_user",
    "password": "cool@aiChat",
    "host": "localhost",
    "port": "5432",
    "database": "chat_db_main2",
    "sslmode": "disable"
}

EMBEDDING_MODEL_NAME = 'nomic-ai/nomic-embed-text-v1.5'

try:
    embedding_model = SentenceTransformer(EMBEDDING_MODEL_NAME, trust_remote_code=True)
    print(f"Loaded embedding model: {EMBEDDING_MODEL_NAME}")
except Exception as e:
    print(f"Failed loading embedding model: {e}")
    embedding_model = None

def fetch_from_xlsx():
    print(f"Fetching pages from XLSX: {XLSX_FILE_PATH}")
    all_records = []
    try:
        df = pd.read_excel(XLSX_FILE_PATH)
        df = df.where(pd.notnull(df), None)
        required_columns = ['URL', 'Title', 'Content']
        for col in required_columns:
            if col not in df.columns:
                print(f"Missing required column '{col}' in XLSX file")
                return []
        for row in df.itertuples(index=False):
            content = getattr(row, 'Content', None)
            meta_description = getattr(row, 'Description', None) or ((content[:160] + '...') if content and len(content) > 160 else content)
            record = {
                'url': getattr(row, 'URL', None),
                'title': getattr(row, 'Title', None),
                'content': content,
                'keywords': getattr(row, 'Keywords', None),
                'updatedDate': getattr(row, 'updatedDate', datetime.datetime.now(datetime.timezone.utc)),
                'metaTitle': getattr(row, 'metaTitle', getattr(row, 'Title', None)),
                'metaDescription': meta_description,
                'category': getattr(row, 'category', 'Pages')
            }
            all_records.append(record)
        print(f"Found {len(all_records)} records in XLSX")
    except FileNotFoundError:
        print(f"File not found: {XLSX_FILE_PATH}")
    except Exception as e:
        print(f"Error reading XLSX: {e}")
    return all_records

def chunk_text(text, max_words=200):
    sentences = sent_tokenize(text or '')
    chunks = []
    current_chunk = []
    current_word_count = 0
    for sentence in sentences:
        words_in_sentence = len(sentence.split())
        if current_word_count + words_in_sentence > max_words and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = [sentence]
            current_word_count = words_in_sentence
        else:
            current_chunk.append(sentence)
            current_word_count += words_in_sentence
    if current_chunk:
        chunks.append(' '.join(current_chunk))
    return chunks

def upsert_rag_document_page(conn, record):
    sql = """
    INSERT INTO rag_document_pages (url, title, content, keywords, updated_date, meta_title, meta_description, category)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    ON CONFLICT (url) DO UPDATE SET
        title = EXCLUDED.title,
        content = EXCLUDED.content,
        keywords = EXCLUDED.keywords,
        updated_date = EXCLUDED.updated_date,
        meta_title = EXCLUDED.meta_title,
        meta_description = EXCLUDED.meta_description,
        category = EXCLUDED.category
    RETURNING id;
    """
    with conn.cursor() as cur:
        cur.execute(sql, (
            record['url'],
            record['title'],
            record['content'],
            record['keywords'],
            record['updatedDate'],
            record['metaTitle'],
            record['metaDescription'],
            record['category']
        ))
        return cur.fetchone()[0]

def upsert_rag_chunk(conn, page_id, chunk_index, chunk_text, embedding):
    sql = """
    INSERT INTO rag_chunks (page_id, chunk_index, chunk_text, embedding)
    VALUES (%s, %s, %s, %s::vector)
    ON CONFLICT (page_id, chunk_index) DO UPDATE SET
        chunk_text = EXCLUDED.chunk_text,
        embedding = EXCLUDED.embedding;
    """
    with conn.cursor() as cur:
        cur.execute(sql, (page_id, chunk_index, chunk_text, embedding))

def upsert_with_chunks(records):
    if embedding_model is None:
        print("Embedding model not loaded; cannot proceed.")
        return False
    try:
        conn = psycopg2.connect(**DEST_CONN_DETAILS)
        for idx, record in enumerate(records):
            if idx < 3280:
                continue
            if not record.get('url'):
                print(f"Skipping record at index {idx} due to missing URL")
                continue
            print(f"Processing record {idx+1}/{len(records)}: {record['url']}")
            page_id = upsert_rag_document_page(conn, record)

            chunks = chunk_text(record['content'])
            for chunk_idx, chunk in enumerate(chunks):
                try:
                    emb = embedding_model.encode([chunk])[0].tolist()
                except Exception as e:
                    print(f"Embedding error chunk {chunk_idx} record {idx}: {e}")
                    emb = None
                upsert_rag_chunk(conn, page_id, chunk_idx, chunk, emb)
            conn.commit()
        print(f"Upserted {len(records)} pages with chunks and embeddings.")
        return True
    except Exception as e:
        print(f"Error during upsert: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def main():
    print("--- Starting XLSX Page Sync with Chunking ---")
    records = fetch_from_xlsx()
    if not records:
        print("No records found to sync.")
        return
    upsert_with_chunks(records)
    print("--- XLSX Page Sync Finished ---")

if __name__ == "__main__":
    main()
