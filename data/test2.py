import psycopg2
import os
import datetime
import pandas as pd

os.chdir(os.path.dirname(__file__))

try:
    connection = psycopg2.connect(
        user="ai_chat_user",
        password="cool@aiChat",
        host="localhost",
        port="5434",
        database="chat_db_main"
    )

    cursor = connection.cursor()
    #
    blogs = pd.read_excel("Blogs.xlsx")
    item = 0
    for index, row in blogs.iterrows():
        cursor.execute(
            "INSERT INTO cybro_pages (url, title, content, keywords, updatedDate, metaTitle, metaDescription, category) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
            (f'https://www.cybrosys.com/blog/{row["titleURLLink"]}', row['titleText'], row['contentText'], row['metaKeywords'], row['updatedDate'], row['titleContent'], row['metaDescription'], row['blogtagname'])
        )
        connection.commit()
        item += 1
        print(f"Inserted {row['titleText']} index: {item}")

    # rnd = pd.read_excel("RnD.xlsx")
    # for index, row in rnd.iterrows():
    #     cursor.execute(
    #         "INSERT INTO cybro_pages2 (url, title, content, keywords, updatedDate, metaTitle, metaDescription, category) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
    #         (f'https://www.cybrosys.com/research-and-development/{row["blogUrl"]}', row['titleText'], row['contentText'], row['metaKeywords'], row['updatedDate'], row['titleContent'], row['metaDescription'], row['blogtagname'])
    #     )
    #     connection.commit()
    #     print(f"Inserted {row['titleText']}")
    # question = input("Enter question: ")
    # query = f'''
    # SELECT title FROM cybro_pages
    # ORDER BY vectors <=> pgml.embed('intfloat/e5-base-v2', '{question}')::vector
    # LIMIT 5;
    # '''
    start = datetime.datetime.now()
    cursor.execute(query)
    results = cursor.fetchall()
    for result in results:
        print(result[0])
    print(datetime.datetime.now() - start)

except (Exception, psycopg2.Error) as error:
    print("Error while connecting to PostgreSQL", error)
finally:
    if connection:
        cursor.close()
        connection.close()
        print("PostgreSQL connection is closed")