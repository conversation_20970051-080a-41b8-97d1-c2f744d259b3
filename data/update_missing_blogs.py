import pyodbc
import psycopg2
import nltk
import datetime
from sentence_transformers import SentenceTransformer
from nltk.tokenize import sent_tokenize

try:
    nltk.download('punkt_tab')
    nltk.download('punkt')
except Exception as e:
    print(f"Warning: Could not download NLTK data: {e}")


SRC_CONN_STR = "DRIVER={ODBC Driver 17 for SQL Server};SERVER=cybrosyssql.database.windows.net;DATABASE=cybrosysdbv5;UID=azadmin;PWD=******************"
DEST_CONN_DETAILS = {
    "user": "ai_chat_user",
    "password": "cool@aiChat",
    "host": "localhost",
    "port": "5432",
    "database": "chat_db_main2",
    "sslmode": "disable"
}
EMBEDDING_MODEL_NAME = 'nomic-ai/nomic-embed-text-v1.5'
try:
    embedding_model = SentenceTransformer(EMBEDDING_MODEL_NAME, trust_remote_code=True)
    print(f"Loaded embedding model: {EMBEDDING_MODEL_NAME}")
except Exception as e:
    print(f"Failed to load embedding model: {e}")
    embedding_model = None

FETCH_BLOGS_QUERY = """
SELECT B.titleText, B.updatedDate, B.titleURLLink, B.contentText, B.titleContent, B.blogtagname, B.metaDescription
FROM tbl_Blog B
INNER JOIN tbl_Blogger BG ON B.bloggerId=BG.bloggerId
WHERE B.updatedDate > ?
ORDER BY B.updatedDate DESC
"""
FETCH_BLOG_IN_RAG = """
SELECT url from rag_document_pages;
"""

def fetch_from_main_db(last_sync_date, conn):
    urls = []
    with conn.cursor() as cur:
        cur.execute(FETCH_BLOG_IN_RAG)
        for row in cur.fetchall():
            urls.append(row[0])
    all_records = []
    try:
        with pyodbc.connect(SRC_CONN_STR) as conn:
            cursor = conn.cursor()
            cursor.execute(FETCH_BLOGS_QUERY, last_sync_date)
            for row in cursor.fetchall():
                url = f'https://www.cybrosys.com/blog/{row.titleURLLink}'
                if url in urls:
                    continue
                content = row.contentText
                all_records.append({
                    'url': url,
                    'title': row.titleText,
                    'content': content,
                    'keywords': row.blogtagname,
                    'updatedDate': row.updatedDate,
                    'metaTitle': row.titleText,
                    'metaDescription': row.metaDescription,
                    'category': row.blogtagname
                })
    except Exception as e:
        print(f"Error fetching data: {e}")

    print(f"Total {len(all_records)} records fetched.")
    return all_records


def chunk_text(text, max_words=200):
    """Chunk text into smaller segments. Fallback to simple splitting if NLTK fails."""
    if not text:
        return []
    
    try:
        sentences = sent_tokenize(text)
    except Exception as e:
        print(f"Warning: NLTK sentence tokenization failed: {e}")
        # Fallback to simple sentence splitting
        sentences = text.replace('!', '.').replace('?', '.').split('.')
        sentences = [s.strip() for s in sentences if s.strip()]
    
    chunks = []
    current_chunk = []
    current_word_count = 0

    for sentence in sentences:
        sentence_word_count = len(sentence.split())
        if current_word_count + sentence_word_count > max_words and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = [sentence]
            current_word_count = sentence_word_count
        else:
            current_chunk.append(sentence)
            current_word_count += sentence_word_count
    
    if current_chunk:
        chunks.append(' '.join(current_chunk))
    
    return chunks

def upsert_rag_document_page(conn, record):
    """Upsert page metadata, return page id."""
    upsert_page_sql = """
    INSERT INTO rag_document_pages (url, title, content, keywords, updated_date, meta_title, meta_description, category)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    ON CONFLICT (url) DO UPDATE SET
        title = EXCLUDED.title,
        content = EXCLUDED.content,
        keywords = EXCLUDED.keywords,
        updated_date = EXCLUDED.updated_date,
        meta_title = EXCLUDED.meta_title,
        meta_description = EXCLUDED.meta_description,
        category = EXCLUDED.category
    RETURNING id;
    """
    with conn.cursor() as cur:
        cur.execute(upsert_page_sql, (
            record['url'],
            record['title'],
            record['content'],
            record['keywords'],
            record['updatedDate'],
            record['metaTitle'],
            record['metaDescription'],
            record['category']
        ))
        page_id = cur.fetchone()[0]
        return page_id

def upsert_rag_chunk(conn, page_id, chunk_index, chunk_text, embedding):
    """Insert or update chunk embedding linked to page."""
    upsert_chunk_sql = """
    INSERT INTO rag_chunks (page_id, chunk_index, chunk_text, embedding)
    VALUES (%s, %s, %s, %s::vector)
    ON CONFLICT (page_id, chunk_index) DO UPDATE SET
        chunk_text = EXCLUDED.chunk_text,
        embedding = EXCLUDED.embedding;
    """
    with conn.cursor() as cur:
        cur.execute(upsert_chunk_sql, (page_id, chunk_index, chunk_text, embedding))


def upsert_with_chunks(records):
    if embedding_model is None:
        print("Embedding model not loaded; cannot upsert with embeddings.")
        return False
    
    conn = None
    try:
        conn = psycopg2.connect(**DEST_CONN_DETAILS)
        for idx, record in enumerate(records):
            print(f"Processing record {idx+1} / {len(records)}: {record['url']}")

            # Upsert page and get page_id
            page_id = upsert_rag_document_page(conn, record)

            # Chunk content safely
            chunks = chunk_text(record['content'] or '', max_words=200)
            
            # Insert each chunk with embedding
            for chunk_idx, chunk in enumerate(chunks):
                try:
                    embedding = embedding_model.encode([chunk])[0].tolist()
                except Exception as e:
                    print(f"Error embedding chunk {chunk_idx} of record {idx}: {e}")
                    embedding = None
                
                if embedding:  # Only insert if embedding was successful
                    upsert_rag_chunk(conn, page_id, chunk_idx, chunk, embedding)
            
            conn.commit()
        
        print(f"Successfully upserted {len(records)} pages and their chunks.")
        return True
    
    except Exception as e:
        print(f"Error during upsert: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def main():
    print("--- Starting Blog Sync with Chunking ---")
    last_sync_date = datetime.datetime(1970, 1, 1)
    conn = psycopg2.connect(**DEST_CONN_DETAILS)

    records = fetch_from_main_db(last_sync_date, conn)

    if not records:
        print("No new records to sync.")
    else:
        upsert_with_chunks(records)
    print("--- Blog Sync Finished ---")

if __name__ == "__main__":
    main()