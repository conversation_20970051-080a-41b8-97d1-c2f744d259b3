import datetime

import meilisearch
import json
import pandas as pd
from pandas import Timestamp
import os

os.chdir(os.path.dirname(__file__))

MEILI_SERVER_URL = "http://localhost:7700"
MEILI_MASTER_KEY = "@Musthafa786"
INDEX_NAME = "cybrosys"

try:
    client = meilisearch.Client(MEILI_SERVER_URL, MEILI_MASTER_KEY)
    # Check if <PERSON>lisearch is running and accessible
    if not client.is_healthy():
        print(f"Meilisearch is not healthy at {MEILI_SERVER_URL}. Please ensure it's running.")
        exit()
    print(f"Successfully connected to <PERSON><PERSON>ear<PERSON> at {MEILI_SERVER_URL}")
except Exception as e:
    print(f"Could not connect to Mei<PERSON>earch: {e}")
    exit()


def index_website_data(documents):
    """
    Adds or updates documents in the specified Meilisearch index.
    'documents' should be a list of dictionaries.
    Each dictionary must have a unique 'id' field (or whatever you set as primaryKey).
    """
    try:
        index = client.index(INDEX_NAME)
        index.delete_all_documents()
        print(f"Using index: '{INDEX_NAME}'")

        # Optionally, set a primary key if you haven't already
        # (only needed if you want a specific field other than 'id' or if the index is new)
        # If the index already exists and has a primary key, this might return an error or be ignored.
        # try:
        #     index.update_settings({'primaryKey': 'url'})
        #     print(f"Set 'url' as the primary key for index '{INDEX_NAME}'.")
        # except Exception as e:
        #     print(f"Note: Could not set primary key (might already be set or other issue): {e}")

        print(f"Adding/updating {len(documents)} documents...")
        task = index.add_documents(documents)
        print(f"  Task ID for adding documents: {task.task_uid}")

        # Wait for the task to complete (optional, but good for scripts)
        client.wait_for_task(task.task_uid, timeout_in_ms=500000)  # Adjust timeout as needed
        task_status = client.get_task(task.task_uid)

        if task_status.status == 'succeeded':
            print("  Documents added/updated successfully!")
        else:
            print(f"  Failed to add documents. Status: {task_status.status}")
            print(f"  Error details: {task_status.error}")

        settings_update_task = index.update_settings({
            'searchableAttributes': [
                'title',
                'content',
                'keywords',
                'metaDescription',
                'metaTitle'
            ],
            'filterableAttributes': [
                'last_modified_date'
            ],
            'sortableAttributes': [
                'last_modified_date'
            ],
            'rankingRules': [
                "words",
                "typo",
                "proximity",
                "attribute",
                "sort",
                "exactness"
            ]
        })
        print(f"  Task ID for updating settings: {settings_update_task.task_uid}")
        client.wait_for_task(settings_update_task.task_uid)
        print("  Index settings updated (searchable, filterable, sortable attributes).")
    except Exception as e:
        print(f"An error occurred during indexing: {e}")


def search_content(query, limit=5):
    """
    Searches the Meilisearch index for the given query.
    """
    try:
        index = client.index(INDEX_NAME)
        search_results = index.search(query, {'limit': limit})
        print(search_results, len(search_results))
        print(f"Found {search_results.estimated_total_hits} potential results.")
        print("Top results:")
        for i, hit in enumerate(search_results.hits):
            print(f"  {i + 1}. Title: {hit.get('title', 'N/A')}")
            print(f"     URL: {hit.get('url', 'N/A')}")
            print(f"     Snippet (first 100 chars of content): {hit.get('content', '')[:100]}...")
            # To get highlighted results, you need to configure attributesToHighlight in search parameters
            # and ensure those attributes are in searchableAttributes.
            # Example:
            # search_params = {
            #     'limit': limit,
            #     'attributesToHighlight': ['title', 'content'],
            #     'highlightPreTag': '<em>',
            #     'highlightPostTag': '</em>'
            # }
            # search_results = index.search(query, search_params)
            # print(f"     Formatted: {hit.get('_formatted', {}).get('title', 'N/A')}")
        return search_results.hits
    except Exception as e:
        print(f"An error occurred during search: {e}")
        return []


# if __name__ == "__main__":
#     docs = []
#     blogs = pd.read_excel("Blogs.xlsx")
#     for index, row in blogs.iterrows():
#         timeStamp: Timestamp = row['updatedDate']
#         doc = {
#             'id': row["titleURLLink"],
#             'url': f'https://www.cybrosys.com/blog/{row["titleURLLink"]}',
#             'title': row['titleText'],
#             'content': row['contentText'],
#             'keywords': row['metaKeywords'].split(',') if type(row['metaKeywords']) == str else [],
#             'last_modified_date': timeStamp.timestamp(),
#             'metaTitle': row['titleContent'],
#             'metaDescription': row['metaDescription'],
#             'category': [tag for tag in row['blogtagname'].split(',') if tag] if type(row['blogtagname']) == str else []
#         }
#         docs.append(doc)
#     # for index, row in pd.read_excel('RnD.xlsx').iterrows():
#     #     doc = {
#     #         'id': row["blogUrl"],
#     #         'url': f'https://www.cybrosys.com/research-and-development/{row["blogUrl"]}',
#     #         'title': row['titleText'],
#     #         'content': row['contentText'],
#     #         'keywords': row['metaKeywords'].split(',') if type(row['metaKeywords']) == str else [],
#     #         'last_modified_date': row['updatedDate'].timestamp() if type(row['updatedDate']) == datetime.datetime else datetime.datetime.now().timestamp(),
#     #         'metaTitle': row['metaTitle'],
#     #         'metaDescription': row['metaDescription'],
#     #         'category': [tag for tag in row['blogtagname'].split(',') if tag] if type(row['blogtagname']) == str else []
#     #     }
#     #     docs.append(doc)
#     index_website_data(docs)

print(search_content("What is Odoo18?"))
