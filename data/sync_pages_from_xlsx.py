import psycopg2
import pandas as pd
import os
import datetime
from sentence_transformers import SentenceTransformer

# --- CONFIGURATION ---
# Path to the source XLSX file
XLSX_FILE_PATH = './pages2.xlsx'
LAST_INDEXED_FILE_PATH = './last_indexed.txt'

# Destination DB (PostgreSQL) Connection Details
DEST_CONN_DETAILS = {
    "user": "ai_chat_user",
    "password": "cool@aiChat",
    "host": "localhost",
    "port": "5432",
    "database": "chat_db_main",
    "sslmode": "disable"
}

# Initialize the SentenceTransformer model globally
# This will download the model the first time it's run
EMBEDDING_MODEL_NAME = 'nomic-ai/nomic-embed-text-v1.5'
try:
    embedding_model = SentenceTransformer(EMBEDDING_MODEL_NAME, trust_remote_code=True)
    print(f"Successfully loaded embedding model: {EMBEDDING_MODEL_NAME}")
except Exception as e:
    print(f"Error loading embedding model {EMBEDDING_MODEL_NAME}: {e}")
    embedding_model = None # Set to None if loading fails

def fetch_from_xlsx():
    """Fetches pages from the specified XLSX file."""
    print(f"Fetching pages from: {XLSX_FILE_PATH}")
    all_records = []
    try:
        # Read the excel file. Assumes the first sheet is the one with the data.
        df = pd.read_excel(XLSX_FILE_PATH)
        
        # Replace NaN with None for easier handling
        df = df.where(pd.notnull(df), None)

        # IMPORTANT: Assumes column names in the XLSX file.
        # Please adjust these if your column names are different.
        required_columns = ['URL', 'Title', 'Content']
        for col in required_columns:
            if col not in df.columns:
                print(f"Error: Missing required column '{col}' in the XLSX file.")
                return []

        for row in df.itertuples(index=False):
            # Map excel columns to the record dictionary
            # Using getattr to handle potentially missing optional columns gracefully
            content = getattr(row, 'Content', None)
            meta_description = getattr(row, 'Description', None) or ((content[:160] + '...') if content and len(content) > 160 else content)
            
            record = {
                'url': getattr(row, 'URL', None),
                'title': getattr(row, 'Title', None),
                'content': content,
                'keywords': getattr(row, 'Keywords', None),
                'updatedDate': getattr(row, 'updatedDate', datetime.datetime.now(datetime.timezone.utc)), # Use current time if not provided
                'metaTitle': getattr(row, 'metaTitle', getattr(row, 'Title', None)),
                'metaDescription': meta_description,
                'category': getattr(row, 'category', 'Pages') # Default category
            }
            all_records.append(record)
            
        print(f"Found {len(all_records)} records in the XLSX file.")

    except FileNotFoundError:
        print(f"Error: The file was not found at {XLSX_FILE_PATH}")
    except Exception as error:
        print(f"Error while reading the XLSX file: {error}")
        
    return all_records

def upsert_to_local_db(records):
    """Inserts or updates records into the local PostgreSQL database."""
    if not records:
        return True

    if embedding_model is None:
        print("Embedding model not loaded. Skipping embedding generation.")
        return False # Indicate failure if model is not available

    upsert_query = """
    INSERT INTO cybro_pages (url, title, content, keywords, updated_date, meta_title, meta_description, category, vectors) 
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s::vector)
    ON CONFLICT (url) DO UPDATE SET
        title = EXCLUDED.title,
        content = EXCLUDED.content,
        keywords = EXCLUDED.keywords,
        updated_date = EXCLUDED.updated_date,
        meta_title = EXCLUDED.meta_title,
        meta_description = EXCLUDED.meta_description,
        category = EXCLUDED.category,
        vectors = EXCLUDED.vectors;
    """
    
    conn = None
    success = False
    try:
        conn = psycopg2.connect(**DEST_CONN_DETAILS)
        cursor = conn.cursor()
        processed_count = 0
        
        for i, record in enumerate(records):
            if not record.get('url'):
                print(f"Skipping record at index {i} due to missing URL.")
                continue

            # Prepare text for embedding
            text_to_embed = f"search_document: {record['title'] or ''}  {record['metaDescription'] or ''} {record['content'] or ''} {record['keywords'] or ''}"
            
            # Generate embedding
            try:
                embedding = embedding_model.encode([text_to_embed])[0].tolist()
            except Exception as e:
                print(f"Error generating embedding for {record['url']}: {e}. Skipping embedding for this record.")
                embedding = None # Set embedding to None if generation fails
            
            params = (
                record['url'],
                record['title'].strip(),
                record['content'],
                record['keywords'],
                record['updatedDate'],
                record['metaTitle'],
                record['metaDescription'],
                record['category'],
                embedding # Add the embedding here
            )
            cursor.execute(upsert_query, params)
            processed_count += 1
            print(f"Processed record {processed_count} of {len(records)}")
            conn.commit()
        print(f"Successfully upserted {processed_count} records with embeddings.")
        success = True
    except (psycopg2.Error, Exception) as error:
        print(f"Error while connecting to PostgreSQL: {error}")
        open(LAST_INDEXED_FILE_PATH, 'w').write(f'''
{processed_count}
{error}
        ''')
    finally:
        if conn:
            cursor.close()
            conn.close()
    return success

def main():
    """Main function to run the sync process."""
    print("--- Starting Page Sync Process from XLSX ---")
    
    records_to_sync = fetch_from_xlsx()
    
    if not records_to_sync:
        print("No records found in the XLSX file to sync.")
    else:
        upsert_to_local_db(records_to_sync)
            
    print("--- Page Sync Process Finished ---")

if __name__ == "__main__":
    main()
