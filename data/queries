Create table cybro_pages(id serial primary key, url Text, title Text, content Text, keywords Text, updatedDate timestamptz, metaTitle Text, metaDescription Text, category Text, vectors vector(768));

CREATE OR REPLACE FUNCTION generate_summary_embedding()
RETURNS TRIGGER AS $$
BEGIN
    NEW.vectors := pgml.embed(
        'intfloat/e5-base-v2',
        NEW.content
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger that calls this function on INSERT
CREATE TRIGGER embed_summary_on_insert
BEFORE INSERT ON cybro_pages
FOR EACH ROW
EXECUTE FUNCTION generate_summary_embedding();

-- If you're using psql, you can run this directly (assuming cybro_pages exists):

INSERT INTO cybro_pages (
  url,
  title,
  content,
  keywords,
  updatedDate,
  metaTitle,
  metaDescription,
  category
)
VALUES (
  'https://www.cybrosys.com/blog/odoo-13-inventory',
  'Odoo 13 Inventory For Your Business',
  'Odoo 13 inventory features include refactored shipping methods, easy inventory adjustments, strong valuation reports, new forecasted stock reports.',
  'Functional,Odoo 13,Warehouse,',
  '2019-11-17 20:29:55.217',
  'Odoo 13 Inventory For Your Business',
  'Odoo 13 inventory features include refactored shipping methods, easy inventory adjustments, strong valuation reports, new forecasted stock reports.',
  'Functional,Odoo 13,Warehouse,'
)
ON CONFLICT (url) DO UPDATE
SET
  title           = EXCLUDED.title,
  content         = EXCLUDED.content,
  keywords        = EXCLUDED.keywords,
  updatedDate     = EXCLUDED.updatedDate,
  metaTitle       = EXCLUDED.metaTitle,
  metaDescription = EXCLUDED.metaDescription,
  category        = EXCLUDED.category;

34640 - chunks - 34747
4501 - pages - 4518