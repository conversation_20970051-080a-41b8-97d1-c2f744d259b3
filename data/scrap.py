import requests
import openpyxl
from bs4 import BeautifulSoup
import re

LINK_FILE = "links.xlsx"
EXCEL_FILE = "pages2.xlsx"
Links = []
AvoidLinks = ['/blog/', '/slides/', '/odoo-apps/', '/webstories/', '/research-and-development/']


def get_links():
    wb1 = openpyxl.load_workbook(LINK_FILE)
    ws1 = wb1.active
    for row in ws1.iter_rows(min_row=2, values_only=True):
        if not row[0]:
            continue
        if any(sub in row[1] for sub in AvoidLinks):
            continue
        Links.append(row[1])
    wb1.close()


get_links()


def get_content(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    title1 = soup.find('title').text
    content1 = soup.find('body')
    for script in content1(["script", "style", "nav", "header", "footer"]):
        script.decompose()
    content1 = re.sub(r'\s+', ' ', content1.text).strip()
    description1 = soup.find('meta', {'name': 'description'}).get('content')
    keywords1 = soup.find('meta', {'name': 'keywords'}).get('content')
    return title1, content1, description1, keywords1


wb = openpyxl.Workbook()
ws = wb.active


def save_content(url, title1, content1, description1, keywords1):
    ws.append([url, title1, content1, description1, keywords1])
    wb.save(EXCEL_FILE)
    wb.close()

# heading
ws.append(['URL', 'Title', 'Content', 'Description', 'Keywords'])


print(f"Total links: {len(Links)}")
index = 1
for link in Links:
    print(f"Scraping {link} ({index} of {len(Links)})")
    try:
        title, content, description, keywords = get_content(link)
        save_content(link, title, content, description, keywords)
        print(f"Scraped {link}")
    except Exception as e:
        print(f"Failed to scrape {link}: {e}")
    index += 1
