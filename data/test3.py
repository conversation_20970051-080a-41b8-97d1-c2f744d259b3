import psycopg2
import os
import datetime
import pandas as pd

os.chdir(os.path.dirname(__file__))

try:
    connection = psycopg2.connect(
        user="ai_chat_user",
        password="cool@aiChat",
        host="localhost",
        port="5434",
        database="chat_db_main"
    )

    cursor = connection.cursor()
    #
    blogs = pd.read_excel("pages_h.xlsx")
    item = 0
    for index, row in blogs.iterrows():
        cursor.execute(
            "INSERT INTO cybro_pages (url, title, content, keywords, updatedDate, metaTitle, metaDescription, category) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
            (row["Url"], row['Title'], row['Content'], row['Keywords'], datetime.datetime.now(), '', row['metaDescription'], '')
        )
        connection.commit()
        item += 1
        print(f"Inserted {row['Title']} index: {item}")

except (Exception, psycopg2.Error) as error:
    print("Error while connecting to PostgreSQL", error)
finally:
    if connection:
        cursor.close()
        connection.close()
        print("PostgreSQL connection is closed")