import psycopg2
import pyodbc
import os
import datetime
from sentence_transformers import SentenceTransformer

# --- CONFIGURATION ---
# Path to the file that stores the timestamp of the last successful sync
LAST_SYNC_FILE = os.path.join(os.path.dirname(__file__), 'last_sync.txt')

# Source DB (MS SQL Server) Connection String
# IMPORTANT: For security, consider moving this to environment variables in a production environment
SRC_CONN_STR = "DRIVER={ODBC Driver 17 for SQL Server};SERVER=cybrosyssql.database.windows.net;DATABASE=cybrosysdbv5;UID=azadmin;PWD=******************"

# Destination DB (PostgreSQL) Connection Details
DEST_CONN_DETAILS = {
    "user": "ai_chat_user",
    "password": "cool@aiChat",
    "host": "localhost",
    "port": "5432",
    "database": "chat_db_main",
    "sslmode": "disable"
}

# Initialize the SentenceTransformer model globally
# This will download the model the first time it's run
EMBEDDING_MODEL_NAME = 'nomic-ai/nomic-embed-text-v1.5'
try:
    embedding_model = SentenceTransformer(EMBEDDING_MODEL_NAME, trust_remote_code=True)
    print(f"Successfully loaded embedding model: {EMBEDDING_MODEL_NAME}")
except Exception as e:
    print(f"Error loading embedding model {EMBEDDING_MODEL_NAME}: {e}")
    embedding_model = None # Set to None if loading fails

# SQL Query to fetch regular blogs from the source database
FETCH_BLOGS_QUERY = """
SELECT 
    B.titleText,
    B.updatedDate,
    B.titleURLLink,
    B.titleContent,
    B.blogtagname
FROM tbl_Blog B
INNER JOIN tbl_Blogger BG ON B.bloggerId=BG.bloggerId            
WHERE B.updatedDate > ?
ORDER BY B.updatedDate ASC
"""

# SQL Query to fetch R&D blogs from the source database
FETCH_RND_BLOGS_QUERY = """
SELECT [entryId]
      ,[titleText]
      ,[contentText]
      ,[publishedDate]
      ,[publishedBy]
      ,[updatedDate]
      ,[blogUrl]
      ,[imgUrl]
      ,[metaTitle]
      ,[metaDescription]
      ,[metaKeywords]
      ,[titleContent]
      ,[blogtagname]
      ,[blogschema]
      ,[youtubeImg]
  FROM [dbo].[tbl_RnDBlog]
  WHERE updatedDate > ?
  ORDER BY publishedDate desc
"""

def get_last_sync_date():
    """Reads the last sync date from the file, or returns a default old date."""
    try:
        with open(LAST_SYNC_FILE, 'r') as f:
            return datetime.datetime.fromisoformat(f.read().strip())
    except (FileNotFoundError, ValueError):
        # If file not found or empty, return a date in the distant past
        # to ensure all records are fetched on the first run.
        return datetime.datetime(1970, 1, 1)

def update_last_sync_date(sync_date):
    """Writes the current sync date to the file."""
    with open(LAST_SYNC_FILE, 'w') as f:
        f.write(sync_date.isoformat())
    print(f"Updated last sync date to: {sync_date.isoformat()}")

def fetch_from_main_db(last_sync_date):
    """Fetches new or updated blogs from the main SQL Server database."""
    print(f"Fetching blogs updated after: {last_sync_date.isoformat()}")
    all_records = []
    try:
        with pyodbc.connect(SRC_CONN_STR) as connection:
            cursor = connection.cursor()

            # Fetch from tbl_Blog
            cursor.execute(FETCH_BLOGS_QUERY, last_sync_date)
            blog_records = cursor.fetchall()
            for row in blog_records:
                url = f'https://www.cybrosys.com/blog/{row.titleURLLink}'
                content = row.titleContent
                meta_description = (content[:160] + '...') if content and len(content) > 160 else content
                all_records.append({
                    'url': url,
                    'title': row.titleText,
                    'content': content,
                    'keywords': row.blogtagname,
                    'updatedDate': row.updatedDate,
                    'metaTitle': row.titleText,
                    'metaDescription': meta_description,
                    'category': row.blogtagname
                })
            print(f"Found {len(blog_records)} regular blogs.")

            # Fetch from tbl_RnDBlog
            cursor.execute(FETCH_RND_BLOGS_QUERY, last_sync_date)
            rnd_blog_records = cursor.fetchall()
            for row in rnd_blog_records:
                content = row.contentText
                url = f"https://www.cybrosys.com/research-and-development/{row.blogUrl}"
                meta_description = row.metaDescription if row.metaDescription else ((content[:160] + '...') if content and len(content) > 160 else content)
                all_records.append({
                    'url': url,
                    'title': row.titleText,
                    'content': content,
                    'keywords': row.blogtagname,
                    'updatedDate': row.updatedDate,
                    'metaTitle': row.metaTitle if row.metaTitle else row.titleText,
                    'metaDescription': meta_description,
                    'category': "RnD Blog"
                })
            print(f"Found {len(rnd_blog_records)} R&D blogs.")

            print(f"Total {len(all_records)} new/updated blogs.")

    except (pyodbc.Error, Exception) as error:
        print(f"Error while connecting to SQL Server: {error}")
    return all_records

def upsert_to_local_db(records):
    """Inserts or updates records into the local PostgreSQL database."""
    if not records:
        return True

    if embedding_model is None:
        print("Embedding model not loaded. Skipping embedding generation.")
        return False # Indicate failure if model is not available

    upsert_query = """
    INSERT INTO cybro_pages (url, title, content, keywords, updated_date, meta_title, meta_description, category, vectors) 
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s::vector)
    ON CONFLICT (url) DO UPDATE SET
        title = EXCLUDED.title,
        content = EXCLUDED.content,
        keywords = EXCLUDED.keywords,
        updated_date = EXCLUDED.updated_date,
        meta_title = EXCLUDED.meta_title,
        meta_description = EXCLUDED.meta_description,
        category = EXCLUDED.category,
        vectors = EXCLUDED.vectors;
    """
    
    conn = None
    success = False
    try:
        conn = psycopg2.connect(**DEST_CONN_DETAILS)
        cursor = conn.cursor()
        index = 1
        
        for record in records:
            # Prepare text for embedding
            text_to_embed = f"search_document: {record['title'] or ''} {record['content'] or ''} {record['keywords'] or ''}"
            
            # Generate embedding
            try:
                embedding = embedding_model.encode([text_to_embed])[0].tolist()
            except Exception as e:
                print(f"Error generating embedding for {record['url']}: {e}. Skipping embedding for this record.")
                embedding = None # Set embedding to None if generation fails
            params = (
                record['url'],
                record['title'],
                record['content'],
                record['keywords'],
                record['updatedDate'],
                record['metaTitle'],
                record['metaDescription'],
                record['category'],
                embedding # Add the embedding here
            )
            cursor.execute(upsert_query, params)
            print(f"Processed record {index} of {len(records)}")
            index += 1
        
        conn.commit()
        print(f"Successfully upserted {len(records)} records with embeddings.")
        success = True
    except (psycopg2.Error, Exception) as error:
        print(f"Error while connecting to PostgreSQL: {error}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            cursor.close()
            conn.close()
    return success

def main():
    """Main function to run the sync process."""
    print("--- Starting Blog Sync Process ---")
    last_sync_date = get_last_sync_date()
    
    # Record the time before fetching, so we don't miss any updates that happen during the sync
    current_sync_time = datetime.datetime.now(datetime.timezone.utc)

    records_to_sync = fetch_from_main_db(last_sync_date)
    
    if not records_to_sync:
        print("No new records to sync.")
    else:
        if upsert_to_local_db(records_to_sync):
            # Only update the sync date if the upsert was successful
            update_last_sync_date(current_sync_time)
            
    print("--- Blog Sync Process Finished ---")

if __name__ == "__main__":
    main()