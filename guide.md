# Guide: Syncing Blogs from Main Server

This guide provides instructions on how to set up and run the script to synchronize blog data from the main SQL Server database to your local PostgreSQL database.

## Server Setup

This section explains how to run the application as a service on a Linux server using the provided `cybro-chat.service` file.

### 1. Prerequisites

- A Linux server with systemd.
- Node.js and npm installed globally.
- The application files cloned or copied to a directory on the server (e.g., `/home/<USER>/AI/cybro-ai-chat-main`).

### 2. Configure the Service File

Before installing the service, you need to edit the `cybro-chat.service` file to match your server's environment.

1.  **Open the service file:**
    ```bash
    nano /home/<USER>/AI/cybro-ai-chat-main/cybro-chat.service
    ```
2.  **Update User and Group:**
    Change the `User` and `Group` directives to a non-root user on your server. It is recommended to run services under a dedicated user for security reasons.
    ```ini
    [Service]
    User=your_user
    Group=your_group
    ```
    Replace `your_user` and `your_group` with the appropriate username and group.

### 3. Install and Start the Service

1.  **Copy the service file to the systemd directory:**
    ```bash
    sudo cp /home/<USER>/AI/cybro-ai-chat-main/cybro-chat.service /etc/systemd/system/
    ```
2.  **Reload the systemd daemon:**
    This command tells systemd to re-read its configuration files.
    ```bash
    sudo systemctl daemon-reload
    ```
3.  **Enable the service to start on boot:**
    ```bash
    sudo systemctl enable cybro-chat.service
    ```
4.  **Start the service immediately:**
    ```bash
    sudo systemctl start cybro-chat.service
    ```

### 4. Managing the Service

Once the service is running, you can manage it using the following `systemctl` commands:

-   **Check the status of the service:**
    ```bash
    sudo systemctl status cybro-chat.service
    ```
-   **View the logs:**
    If you need to debug the service, you can view its logs.
    ```bash
    sudo journalctl -u cybro-chat.service -f
    ```
    The `-f` flag follows the log output in real-time.

-   **Stop the service:**
    ```bash
    sudo systemctl stop cybro-chat.service
    ```

-   **Restart the service:**
    If you make changes to the application code, you'll need to restart the service for the changes to take effect.
    ```bash
    sudo systemctl restart cybro-chat.service
    ```

The script is designed to be run on a schedule to keep your local `cybro_pages` table updated with the latest content from the main server.

## How it Works

The script (`data/sync_blogs_from_server.py`) performs the following steps:
1.  **Checks Last Sync Time**: It reads a timestamp from `data/last_sync.txt`. This tells the script the last time it ran successfully. On the first run, the file won't exist, so it will fetch all blogs.
2.  **Fetches New Data**: It connects to the main SQL Server and fetches all blogs that have been published or updated since the last sync time.
3.  **Upserts Data**: It connects to your local PostgreSQL database and performs an "upsert" operation:
    *   If a blog (identified by its URL) is new, it inserts a new row.
    *   If a blog already exists, it updates the existing row with the latest information.
4.  **Updates Sync Time**: If the data is saved successfully, it updates the `data/last_sync.txt` file with the current time, ready for the next run.

## 1. Dependencies

This script requires two Python libraries: `psycopg2` (which you already have) and a new one, `pyodbc`, for connecting to SQL Server. You also need to install the Microsoft ODBC driver for your operating system.

### 1.1. Install Python Library (`pyodbc`)

Open your terminal and run the following command to install the library:

```bash
pip install pyodbc
```

### 1.2. Install Microsoft ODBC Driver (for Linux)

The `pyodbc` library needs the official Microsoft ODBC driver to communicate with the SQL Server.

Follow these steps to install it on a Debian-based Linux distribution (like Ubuntu):

```bash
# Download the Microsoft repository configuration
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
curl https://packages.microsoft.com/config/debian/11/prod.list | sudo tee /etc/apt/sources.list.d/mssql-release.list

# Update your package lists
sudo apt-get update

# Install the driver
# The `ACCEPT_EULA=Y` part is important
sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17
```
For other Linux distributions, please refer to the [official Microsoft documentation](https://docs.microsoft.com/en-us/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server).

## 2. Database Table Requirement

The script uses an `ON CONFLICT (url) DO UPDATE` command to work correctly. This requires the `url` column in your `cybro_pages` table to have a `UNIQUE` constraint.

If you haven't set this up, connect to your PostgreSQL database and run the following SQL command:

```sql
ALTER TABLE cybro_pages ADD CONSTRAINT cybro_pages_url_unique UNIQUE (url);
```

## 3. Configuration

The script `data/sync_blogs_from_server.py` contains connection details at the top. While you can leave them as is, for better security in a production environment, it is highly recommended to load sensitive information like passwords from environment variables instead of hardcoding them in the file.

## 4. Running the Script Manually (Using the Virtual Environment)

To ensure you are using the correct Python dependencies, you should run the script using the Python interpreter from your project's virtual environment (`venv`).

1.  **Activate the virtual environment** (if you haven't already):
    ```bash
    source /home/<USER>/AI/cybro-ai-chat-main/venv/bin/activate
    ```
2.  **Run the script:**
    ```bash
    python /home/<USER>/AI/cybro-ai-chat-main/data/sync_blogs_from_server.py
    ```
3.  **Deactivate the virtual environment** when you are done:
    ```bash
    deactivate
    ```

## 5. Scheduling with Cron (for Automation)

To run the script automatically, you can set up a cron job. It is crucial to use the Python interpreter from your project's `venv` to ensure the correct libraries (`pyodbc`, etc.) are used.

1.  Open the cron table for editing:
    ```bash
    crontab -e
    ```
2.  Add the following line to the file. This example schedules the script to run every day at 2:00 AM.

    ```cron
    0 2 * * * /home/<USER>/AI/cybro-ai-chat-main/venv/bin/python /home/<USER>/AI/cybro-ai-chat-main/data/sync_blogs_from_server.py >> /home/<USER>/AI/cybro-ai-chat-main/data/sync_log.log 2>&1
    ```

**Cron Job Breakdown:**
*   `0 2 * * *`: The schedule (runs at 2:00 AM every day).
*   `/home/<USER>/AI/cybro-ai-chat-main/venv/bin/python`: **This is the most important part.** It's the absolute path to the Python interpreter inside your virtual environment. This ensures the cron job runs with the project's specific dependencies.
*   `/home/<USER>/AI/cybro-ai-chat-main/data/sync_blogs_from_server.py`: The absolute path to the script you want to run.
*   `>> /home/<USER>/AI/cybro-ai-chat-main/data/sync_log.log 2>&1`: This is optional but highly recommended. It redirects all output to a log file so you can review the script's execution history.

## 6. Managing the Cron Job

Once the cron job is set, you can easily modify or remove it.

### 6.1. Changing the Schedule

1.  Open the cron table again:
    ```bash
    crontab -e
    ```
2.  Find the line for the sync script and edit the schedule part (`0 2 * * *`).
    *   **Every 6 hours**: `0 */6 * * *`
    *   **Twice a day (at 1 AM and 1 PM)**: `0 1,13 * * *`
    *   **Every 30 minutes**: `*/30 * * * *`
3.  Save the file and exit. The new schedule will be active immediately.

### 6.2. Stopping the Cron Job

1.  Open the cron table:
    ```bash
    crontab -e
    ```
2.  Find the line for the script.
3.  You can either:
    *   **Comment it out**: Add a `#` at the beginning of the line to disable it.
        ```cron
        # 0 2 * * * /home/<USER>/AI/cybro-ai-chat-main/venv/bin/python ...
        ```
    *   **Delete the line**: Simply remove the entire line.
4.  Save and close the file. The cron job is now removed.