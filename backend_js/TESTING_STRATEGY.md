# Testing Strategy for Node.js Chat Backend

A robust testing strategy is crucial for ensuring the reliability, correctness, and maintainability of the backend application. This document outlines a multi-layered approach to testing.

## 1. Development Environment and Prerequisites

*   **Node.js and npm/yarn/pnpm:** Ensure a consistent Node.js environment.
*   **PostgreSQL Database:** A dedicated test database is highly recommended. For features involving pgML (like vector search in `searchService`), this test database should also have the `pgml` extension enabled and any necessary embedding models loaded.
*   **Environment Variables:** A separate `.env.test` file (or similar mechanism) should be used to configure the application for the test environment (e.g., pointing to a test database, using mock API keys for external services if not mocking them at the code level).
*   **Prisma:** Ensure Prisma migrations can be run against the test database. `prisma migrate reset` can be useful for resetting the test DB before test suites.

## 2. Testing Levels and Approaches

### 2.1. Unit Tests

*   **Goal:** Test individual functions, modules, or classes in isolation to verify their correctness.
*   **Scope:** Focus on pure functions, business logic within services, utility functions, and specific algorithms.
*   **Tools:**
    *   **Test Runner/Framework:** [Jest](https://jestjs.io/) or [Vitest](https://vitest.dev/) (Vitest is often preferred for Vite-based projects but works well for Node.js too, and is ESM first).
    *   **Assertion Library:** Built into Jest/Vitest (e.g., `expect`).
    *   **Mocking:** Jest/Vitest provide powerful mocking capabilities to isolate units from their dependencies (e.g., database, external services).
*   **Key Areas for Unit Tests:**
    *   `utils/sseUtils.ts`: `sendSseEvent` formatting.
    *   `utils/asyncHandler.ts`: Behavior with successful and failing promises.
    *   `services/llmService.ts`:
        *   Parsing logic in `decideSearch` (mocking the OpenAI client call).
        *   Context construction logic in `generateCompletionStream`.
        *   Error handling and fallback logic (mocking OpenAI client responses to simulate errors).
    *   `services/searchService.ts`:
        *   Formatting of results in `getSearchResults` (mocking DB response).
        *   Logic within `logSearch` (mocking Prisma client).
    *   Input validation schemas (if using Zod, test complex custom validations if any).

### 2.2. Integration Tests (API Endpoint Tests)

*   **Goal:** Test the interaction between different parts of the application, primarily focusing on API endpoints. This verifies that controllers, services, database interactions, and request/response cycles work correctly together.
*   **Scope:** Test each API endpoint for successful responses, error responses, input validation, and basic business logic flow.
*   **Tools:**
    *   **Test Runner/Framework:** Jest or Vitest.
    *   **HTTP Request Library:** [Supertest](https://github.com/ladjs/supertest) for making HTTP requests to your Express app directly without needing a running server on a specific port.
    *   **Database Interaction:**
        *   For tests not involving pgML, you could use `jest-mock-extended` to create deep mocks of the Prisma client (`DeepMockProxy<PrismaClient>`).
        *   For tests involving pgML (`searchService`), it's highly recommended to connect to a real test PostgreSQL database with pgML enabled. Reset this database before each test suite or test file.
    *   **Mocking External Services:** Mock `node-fetch` or the `OpenAI` client directly to simulate responses from OpenRouter for `llmService` tests.
*   **Key Areas for Integration Tests:**
    *   **`conversationController.ts` Endpoints:**
        *   `GET /api/v1/chat/history?userId=...`: Test with valid/invalid `userId`, user with/without conversations, correct data format.
        *   `GET /api/v1/chat/history/:conversationId?userId=...`: Test valid/invalid IDs, ownership, inclusion of messages.
        *   `DELETE /api/v1/chat/history/:conversationId?userId=...`: Test successful deletion, deleting non-existent/unauthorized conversations.
    *   **`feedbackController.ts` Endpoint:**
        *   `POST /api/v1/feedback/`: Test valid/invalid request body, creation of new feedback, update of existing feedback, non-existent user/message.
    *   **`chatController.ts` Endpoint (`POST /api/v1/chat/`):**
        *   Test input validation (e.g., using Zod).
        *   Test successful SSE stream initiation.
        *   Mock `llmService` and `searchService` to test the controller's orchestration of the SSE event flow (status, token, end events). This verifies the controller logic itself.
        *   Test error handling (e.g., if a mock service throws an error, does the controller send an SSE error event and close the stream?).
    *   **Full flow for `chatController` (more complex):** Test with real (or more sophisticated mock) `searchService` and `llmService` against a test DB and mock LLM API to ensure the entire sequence works.

### 2.3. End-to-End (E2E) Tests (Conceptual)

*   **Goal:** Test the entire application flow from the perspective of a client making API requests to a running instance of the backend, including its interactions with a real database and potentially live (or near-live) external services.
*   **Scope:** Simulates real user scenarios.
*   **Tools:**
    *   An HTTP client (like `axios` or `node-fetch` in a test script).
    *   A test runner like Jest or Vitest to orchestrate the tests.
    *   Requires a running instance of the backend application, a dedicated test database (with pgML), and potentially a way to manage/mock the OpenRouter API (e.g., a dedicated test API key, or a local mock server like WireMock).
*   **Key Scenarios for E2E Tests:**
    *   Full chat conversation flow: send message, trigger search, receive streamed LLM response with search results integrated.
    *   User history retrieval.
    *   Feedback submission.

## 3. Running Tests

*   Define scripts in `package.json` for running tests (e.g., `test:unit`, `test:integration`).
    *   Example: `"test": "vitest run"`, `"test:watch": "vitest"`
*   Use environment variables (`NODE_ENV=test`, specific `DATABASE_URL` for tests) when running tests.

## 4. Continuous Integration (CI)

*   Integrate test execution into your CI/CD pipeline (e.g., GitHub Actions, GitLab CI) to automatically run tests on every push or pull request.

## 5. pgML and Vector Search Testing Considerations

*   Testing the `searchService.getSearchResults` function accurately requires a PostgreSQL database with the `pgml` extension installed and the `intfloat/e5-base-v2` (or chosen) embedding model available.
*   The `cybro_pages` table must be populated with test data, including pre-computed vector embeddings.
*   Your test environment needs to manage the lifecycle of this test database (creation, schema migration, seeding with test data, teardown/reset). Tools like Docker can be very helpful for creating reproducible PostgreSQL instances with pgML for testing.

This strategy provides a starting point. Adapt and expand it based on your project's specific needs and complexity.
