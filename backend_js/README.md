# Cybro AI Chat - Node.js Backend

This is the Node.js/Express.js backend for the Cybro AI Chat application. It provides API endpoints for chat streaming, conversation history, feedback, and uses PostgreSQL with pgML for semantic search.

## Table of Contents

- [Project Setup](#project-setup)
- [Environment Variables](#environment-variables)
- [Running the Server](#running-the-server)
- [API Endpoints](#api-endpoints)
- [Testing](#testing)
- [Project Structure (Overview)](#project-structure-overview)

## Project Setup

1.  **Prerequisites:**
    *   Node.js (e.g., v18.x or v20.x)
    *   npm, yarn, or pnpm
    *   PostgreSQL server
    *   pgML extension installed and configured in your PostgreSQL instance.
    *   The `intfloat/e5-base-v2` embedding model (or the configured model) available to pgML.
    *   A `cybro_pages` table in your database with `url`, `content`, and `vectors` columns, where `vectors` contains pgML embeddings of the content.

2.  **Clone the repository (if applicable).**

3.  **Navigate to the `backend_node` directory:**
    ```bash
    cd backend_node
    ```

4.  **Install dependencies:**
    ```bash
    npm install
    # or yarn install / pnpm install
    ```

5.  **Set up environment variables:**
    *   Copy the example environment file:
        ```bash
        cp .env.example .env
        ```
    *   Edit the `.env` file with your specific configurations (DATABASE_URL, OPENROUTER_API_KEY, PORT, etc.). See the [Environment Variables](#environment-variables) section for details.

6.  **Run database migrations:**
    This will create the necessary tables based on `prisma/schema.prisma`.
    ```bash
    npx prisma migrate dev --name init 
    # If you already ran init, future migrations might have different names or use `prisma migrate deploy` for production
    ```
    *Ensure your `DATABASE_URL` in `.env` is correctly pointing to your PostgreSQL database before running migrations.*
    
7.  **Generate Prisma Client:**
    (Often run automatically by `prisma migrate dev`, but can be run manually)
    ```bash
    npx prisma generate
    ```

## Environment Variables

The following environment variables are used by the application. Create a `.env` file in the `backend_node` directory based on `.env.example` and fill in the values:

*   `PORT`: The port the server will run on (e.g., 8001).
*   `DATABASE_URL`: PostgreSQL connection string.
    *   Example: `postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public`
*   `OPENROUTER_API_KEY`: Your API key for OpenRouter (for LLM services).
*   `LOG_LEVEL` (Optional): Logging level for Pino (e.g., 'info', 'debug', 'warn', 'error'). Defaults to 'info'.
*   `NODE_ENV` (Optional): Set to `development` or `production`. Influences logging format and potentially other behaviors. Defaults to 'development' for logging if not set.
*   `SITE_URL` (Optional): URL of the frontend, used for HTTP-Referer header in LLM calls (e.g., `http://localhost:3000`).
*   `APP_TITLE` (Optional): Application title, used for X-Title header in LLM calls (e.g., `Cybro AI Chat Node`).
*   `DECISION_LLM_MODEL` (Optional): Specific model name for the search decision LLM (e.g., `qwen/qwq-32b:free`).
*   `CHAT_LLM_MODEL` (Optional): Specific model name for the main chat LLM (e.g., `qwen/qwq-32b:free`).


## Running the Server

*   **Development Mode (with auto-reload):**
    ```bash
    npm run dev
    ```
    The server will typically run on `http://localhost:PORT` (e.g., `http://localhost:8001`).

*   **Production Mode:**
    1.  Build the TypeScript code:
        ```bash
        npm run build
        ```
    2.  Start the server:
        ```bash
        npm start
        ```

## API Endpoints

See `API_DOCUMENTATION.md` for detailed descriptions of API endpoints, request/response schemas, and examples.

**Brief Overview:**
*   `POST /api/v1/chat/`: Initiates a chat and streams responses.
*   `GET /api/v1/chat/history?userId=<uuid>`: Lists conversations for a user.
*   `GET /api/v1/chat/history/:conversationId?userId=<uuid>`: Gets details of a specific conversation.
*   `DELETE /api/v1/chat/history/:conversationId?userId=<uuid>`: Deletes a conversation.
*   `POST /api/v1/feedback/`: Submits feedback for a message.
*   `GET /health`: Health check endpoint.

## Testing

See `TESTING_STRATEGY.md` for details on how to run unit and integration tests.

## Project Structure (Overview)

```
backend_node/
├── prisma/                 # Prisma schema, migrations, generated client
│   ├── schema.prisma
│   └── migrations/
├── src/                    # Source code
│   ├── config/             # Configuration files (logger, etc.)
│   ├── controllers/        # Request handlers for API routes
│   ├── db/                 # Database clients (Prisma, pgPool)
│   ├── middlewares/        # Custom Express middlewares (if any)
│   ├── routes/             # Express route definitions
│   ├── services/           # Business logic (LLM, search, etc.)
│   ├── types/              # Shared TypeScript type definitions (if any created)
│   ├── utils/              # Utility functions (asyncHandler, sseUtils)
│   ├── app.ts              # Express application setup
│   └── server.ts           # Server startup logic
├── .env.example            # Example environment variables
├── package.json
├── tsconfig.json
├── TESTING_STRATEGY.md     # Outline of testing approaches
└── README.md               # This file
```

---
This README provides a basic guide. Refer to specific code files and `API_DOCUMENTATION.md` for more details.
