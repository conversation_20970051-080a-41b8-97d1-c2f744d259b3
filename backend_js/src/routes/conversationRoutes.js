const express = require('express');
const {
  getUserConversations,
  getConversationDetails,
  deleteUserConversation,
} = require('../controllers/conversationController');

const router = express.Router();

router.get('/history', getUserConversations);

router.get('/history/:conversationId', getConversationDetails);

router.delete('/history/:conversationId/delete', deleteUserConversation);

module.exports = router;