const sendSseEvent = (
  res,
  eventName,
  data,
  eventId
) => {
  if (res.writableEnded) {
    return;
  }

  let eventString = '';
  if (eventId) {
    eventString += `id: ${eventId}\n`;
  }
  eventString += `event: ${eventName}\n`;
  eventString += `data: ${JSON.stringify(data)}\n\n`;

  res.write(eventString);

  if (typeof res.flush === 'function') {
    res.flush();
  }
};

module.exports = { sendSseEvent };