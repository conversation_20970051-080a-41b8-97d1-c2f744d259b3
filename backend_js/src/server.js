const app = require('./app');
const prisma = require('./db/prismaClient');
const { disconnectPgPool } = require('./db/pgPool');
const logger = require('./config/logger');

const PORT = process.env.PORT || 8001;

const server = app.listen(PORT, () => {
  logger.info(`Server is running on http://localhost:${PORT}`);
});

const signals = ['SIGINT', 'SIGTERM'];
signals.forEach(signal => {
  process.on(signal, async () => {
    logger.info(`
Received ${signal}, shutting down gracefully...`);
    server.close(async () => {
      logger.info('Server closed.');
      try {
        await prisma.$disconnect();
        logger.info('Prisma Client disconnected successfully.');
      } catch (e) {
        logger.error(e, 'Error disconnecting Prisma Client:');
      }
      try {
        await disconnectPgPool();
        logger.info('pg <PERSON> disconnected successfully.');
      } catch (e) {
        logger.error(e, 'Error disconnecting pg Pool:');
      }
      process.exit(0);
    });
  });
});