const { getPgPool } = require('../db/pgPool');
const logger = require('../config/logger');
const prisma = require('../db/prismaClient');
const { pipeline } = require('@xenova/transformers');

// Use a class to encapsulate the feature extraction pipeline
// and handle lazy loading of the model.
class FeatureExtractionPipeline {
    static task = 'feature-extraction';
    static model = 'nomic-ai/nomic-embed-text-v1.5';
    static instance = null;

    static async getInstance(progress_callback = null) {
        if (this.instance === null) {
            this.instance = pipeline(this.task, this.model, { progress_callback });
        }
        return this.instance;
    }
}

const getSearchResults = async (
  queryText,
  numResults = 3,
  offset = 0
) => {
  logger.info({ queryText, numResults }, 'Executing getSearchResults');

  if (!queryText || queryText.trim() === "") {
    logger.warn('getSearchResults called with empty queryText. Returning empty results.');
    return [];
  }

  const pool = getPgPool();
  let client;

  try {
    client = await pool.connect();
    logger.debug('Connected to pg Pool for search');

    // Generate the embedding
    const extractor = await FeatureExtractionPipeline.getInstance();
    const output = await extractor(`search_query: ${queryText}`, { pooling: 'mean', normalize: true });
    const embedding = `[${output.data.join(',')}]`;


    const sqlQuery = `
      SELECT url, content
      FROM cybro_pages
      ORDER BY vectors <=> $1::vector
      LIMIT $2 OFFSET $3;
    `;

    const values = [embedding, numResults, offset];

    logger.debug({ sqlQuery, values: [embedding.substring(0, 50) + '...', numResults] }, 'Executing vector search query');
    const { rows } = await client.query(sqlQuery, values);
    logger.info({ queryText, resultsCount: rows.length }, 'Search query executed successfully.');

    if (!rows || rows.length === 0) {
      return [];
    }

    const results = rows.map(row => {
      if (!row.url || !row.content) {
        logger.warn({ row }, 'Search result row missing url or content, skipping.');
        return null;
      }
      // Return the full URL and content
      return { 
        url: row.url,
        content: row.content
      };
    }).filter(result => result !== null);

    return results;

  } catch (error) {
    logger.error(error, `Error in getSearchResults for query: "${queryText}"`);
    return [];
  } finally {
    if (client) {
      client.release();
      logger.debug('pg Client released for search');
    }
  }
};

const logSearch = async (data) => {
  const {
    conversationId,
    userQuery,
    generatedSearchQuery,
    searchResultsUrls
  } = data;

  logger.info(
    { conversationId, userQuery, generatedSearchQuery, urlsCount: searchResultsUrls.length },
    'Logging search query'
  );

  try {
    await prisma.searchLog.create({
      data: {
        userQuery: userQuery,
        generatedSearchQuery: generatedSearchQuery,
        searchResultsUrls: searchResultsUrls,
        ...(conversationId && {
          conversation: {
            connect: { id: conversationId }
          }
        })
      },
    });
    logger.info({ conversationId, generatedSearchQuery }, 'Search logged successfully.');
  } catch (error) {
    logger.error(
      error,
      `Failed to log search for conversationId: ${conversationId}, query: "${generatedSearchQuery}"`
    );
  }
};

module.exports = { getSearchResults, logSearch };