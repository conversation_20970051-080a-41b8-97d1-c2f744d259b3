const OpenAI = require('openai');
const logger = require('../config/logger');
const { AbortError, default: pRetry } = require('p-retry');
const { searchToolDefinition, executeSearchTool, executeSearchByUrlTool, searchByUrlToolDefinition } = require('./tools/newSearchTool');

let openAIClientInstance = null;

const getOpenAIClient = () => {
  if (!process.env.OPENAI_API_KEY) {
    logger.error(
      'OPENAI_API_KEY is not set in environment variables. LLM service cannot connect.'
    );
    throw new Error('OPENAI_API_KEY is not configured.');
  }

  if (!openAIClientInstance) {
    try {
      let options = {
        apiKey: process.env.OPENAI_API_KEY,
      };
      if (process.env.OPENAI_API_URL) {
        options['baseURL'] = process.env.OPENAI_API_URL;
      }
      logger.info({options});
      openAIClientInstance = new OpenAI(options);
      logger.info('OpenAI client initialized.');
    } catch (error) {
      logger.error(error, 'Failed to initialize OpenAI client for OpenRouter.');
      throw error;
    }
  }
  return openAIClientInstance;
};

class ProviderError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ProviderError';
  }
}

const DEFAULT_MODEL = process.env.CHAT_LLM_MODEL || 'qwen/qwq-32b:free';

async function* runAgent(userPrompt, conversationId, currentConversationHistory) {
  logger.info(
    { userPrompt, conversationId, historyLength: currentConversationHistory.length },
    'runAgent called'
  );

  const client = getOpenAIClient();
  const tools = [searchToolDefinition];

  const systemMessageContent = `You are Cybro AI, an expert assistant for Cybrosys Technologies, specializing in Odoo, PostgreSQL, and related topics. Your goal is to provide accurate, detailed answers based *exclusively* on information retrieved from the Cybrosys documentation using your search tool.

You must follow this exact process:

1. **Analyze and Plan:**
   - Carefully analyze the user's question.
   - If this chat is a just greeting then greet them back.
   - Formulate an optimal, concise search query most likely to find relevant document chunks from Cybrosys documentation.
   - *Example:* If the user asks "how do I set up a new company in odoo 17?", a good search query would be "odoo 17 setup new company".

2. **Execute Search:**
   - Call the \`search_cybrosys_docs\` tool with your generated query. Start with offset 0. 
   - The output from this will be a part or multiple of a full document.
   - Call the \`search_cybrosys_docs_with_url\` tool with the url of the document to get more details if the given info is not enough (mostly for guids like setup and installations).

3. **Evaluate Search Results:**
   - Examine the chunked search results carefully.
   - Ask: Are the results relevant and on-topic for the user's question?

4. **Decide Next Action:**
   - If results are relevant, proceed.
   - If no relevant results, retry with new queries or incremented offset (up to 2 retries).
   - If still no relevant info after retries, state no relevant docs found.

5. **Synthesize and Cite:**
   - Formulate a detailed answer *only* from retrieved chunks.
   - Cite the URLs of documents you used.`

  let messages = [
    { role: "system", content: systemMessageContent },
    ...currentConversationHistory.map((msg) => ({
      role: msg.role,
      content: msg.content,
      ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
      ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
    })),
    { role: "user", content: userPrompt },
  ];

  let toolOutput = null;

  for (let attempt = 0; attempt < 5; attempt++) {
    try {
      const response = await client.chat.completions.create({
        model: DEFAULT_MODEL,
        messages,
        tools,
        tool_choice: "auto",
        stream: false,
        temperature: 0.7,
        max_tokens: 1000
      });

      const responseMessage = response.choices[0].message;
      messages.push(responseMessage);

      if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
        for (const toolCall of responseMessage.tool_calls) {
          const functionName = toolCall.function.name;
          const functionArgs = JSON.parse(toolCall.function.arguments);

          if (functionName === "search_cybrosys_docs") {
            yield { type: "status", content: `Searching for: ${functionArgs.query}...` };

            toolOutput = await executeSearchTool(
              functionArgs.query,
              functionArgs.offset,
              conversationId,
              userPrompt
            );

            // Detect your no results message
            if (
              typeof toolOutput === "string" &&
              toolOutput.includes("No relevant documents found")
            ) {
              if (attempt < 2) {
                messages.push({
                  role: "tool",
                  tool_call_id: toolCall.id,
                  content: toolOutput,
                });
                messages.push({
                  role: "user",
                  content: `Previous search yielded no relevant documents. Please retry with offset ${
                    functionArgs.offset + 3
                  }.`,
                });
                continue; // Retry with incremented offset
              } else {
                yield {
                  type: "token",
                  content:
                    "I couldn't find any relevant information in the Cybrosys documentation for your query even after multiple attempts. Please try rephrasing your question.",
                };
                yield { type: "end" };
                return;
              }
            }

            // Pass the chunked results as a JSON string back to the LLM
            const toolOutputString = JSON.stringify(toolOutput);
            messages.push({
              tool_call_id: toolCall.id,
              role: "tool",
              content: toolOutputString,
            });
            yield { type: "status", content: "Processing search results..." };
          } else if(functionName === "search_cybrosys_docs_with_url"){
            yield { type: "status", content: `getiing more details from ${functionArgs.url}...` };

            toolOutput = await executeSearchByUrlTool(
              functionArgs.url,
              conversationId,
              userPrompt
            );

            const toolOutputString = JSON.stringify(toolOutput);
            messages.push({
              tool_call_id: toolCall.id,
              role: "tool",
              content: toolOutputString,
            });
            yield { type: "status", content: "Processing search results..." };
          }
           else {
            const errorMsg = `Error: Unknown tool called: ${functionName}`;
            logger.error(errorMsg);
            messages.push({
              tool_call_id: toolCall.id,
              role: "tool",
              content: errorMsg,
            });
          }
        }
      } else if (responseMessage.content) {
        yield* generateCompletionStream(responseMessage.content, messages, !!toolOutput, toolOutput, userPrompt, conversationId);
        return;
      }
    } catch (error) {
      logger.error(error, `Error during agent turn ${attempt + 1}`);

      // Check if this is a 429 rate limit error
      if (error.status === 429 || error.message?.includes('429') || error.message?.toLowerCase().includes('rate limit')) {
        logger.warn('Rate limit error detected (429), falling back to manual search');

        // Perform manual search with the user's original query
        yield { type: "status", content: "AI is currently unavailable due to rate limits. Searching documentation manually..." };

        try {
          const searchResults = await executeSearchTool(userPrompt, 0, conversationId, userPrompt);

          if (searchResults && Array.isArray(searchResults) && searchResults.length > 0) {
            // Extract unique URLs from search results
            const uniqueUrls = [...new Set(searchResults.map(result => result.url))];

            let responseContent = "## AI Currently Unavailable\n\n";
            responseContent += "The AI is currently unavailable due to high demand. However, I found some relevant documentation that might help:\n\n";

            uniqueUrls.forEach((url, index) => {
              responseContent += `${index + 1}. [${url}](${url})\n`;
            });

            responseContent += "\n**Please check these sources for information related to your query.**";

            yield { type: "token", content: responseContent };
            yield { type: "end" };
            return;
          } else {
            yield {
              type: "token",
              content: "## AI Currently Unavailable\n\nThe AI is currently unavailable due to high demand, and I couldn't find relevant documentation for your query. **Please try again later or contact support.**"
            };
            yield { type: "end" };
            return;
          }
        } catch (searchError) {
          logger.error(searchError, 'Error during fallback manual search');
          yield {
            type: "token",
            content: "## AI Currently Unavailable\n\nThe AI is currently unavailable due to high demand. **Please try again later or contact support.**"
          };
          yield { type: "end" };
          return;
        }
      }

      throw new ProviderError(`Agent error: ${error.message || "Unknown error"}`);
    }
  }

  yield* generateCompletionStream(
    "I'm having trouble processing this request. Can you please rephrase it?",
    messages,
    !!toolOutput,
    toolOutput,
    userPrompt,
    conversationId
  );
}

async function* generateCompletionStream(
  initialContent,
  fullConversationHistory,
  toolWasExecuted,
  toolResults,
  userPrompt = null,
  conversationId = null
) {
  logger.info(
    {
      initialContent,
      historyLength: fullConversationHistory.length,
      toolWasExecuted,
      toolResultsLength: toolResults?.length || 0,
    },
    "generateCompletionStream called for final response"
  );

  const client = getOpenAIClient();
  let messagesForFinalCompletion = [...fullConversationHistory];

  if (toolWasExecuted && toolResults) {
    messagesForFinalCompletion.push({
      role: "user",
      content: `Based on the search results I provided (consider these as my findings), please give a detailed and comprehensive answer to the original question. Cite the sources at the end. Search results: ${JSON.stringify(
        toolResults
      )}`,
    });
  }

  try {
    const stream = await pRetry(
      () =>
        client.chat.completions.create({
          model: DEFAULT_MODEL,
          messages: messagesForFinalCompletion,
          stream: true,
          temperature: 0.7,
          max_tokens: 2000,
        }),
      {
        retries: 2,
        minTimeout: 1000,
        factor: 2,
        onFailedAttempt: (error) => {
          logger.warn(
            `LLM final stream creation attempt ${error.attemptNumber} failed. Retries left: ${error.retriesLeft}. Error: ${error.message}`
          );
          if (error.message?.toLowerCase().includes("context_length_exceeded")) {
            throw error;
          }
          // Don't retry on 429 errors, let them bubble up to be handled by the catch block
          if (error.status === 429 || error.message?.includes('429') || error.message?.toLowerCase().includes('rate limit')) {
            throw error;
          }
          if (!(error instanceof ProviderError)) {
            throw new AbortError(error.message);
          }
        },
      }
    );

    for await (const chunk of stream) {
      if (chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta?.content) {
        yield { type: "token", content: chunk.choices[0].delta.content };
      }
    }
    yield { type: "end" };
  } catch (iterationError) {
    logger.error(iterationError, "Error during LLM stream iteration for final response.");

    // Check if this is a 429 rate limit error
    if (iterationError.status === 429 || iterationError.message?.includes('429') || iterationError.message?.toLowerCase().includes('rate limit')) {
      logger.warn('Rate limit error detected (429) in final completion stream, falling back to manual search');

      if (userPrompt && conversationId) {
        try {
          const searchResults = await executeSearchTool(userPrompt, 0, conversationId, userPrompt);

          if (searchResults && Array.isArray(searchResults) && searchResults.length > 0) {
            // Extract unique URLs from search results
            const uniqueUrls = [...new Set(searchResults.map(result => result.url))];

            let responseContent = "## AI Currently Unavailable\n\n";
            responseContent += "The AI is currently unavailable due to high demand. However, I found some relevant documentation that might help:\n\n";

            uniqueUrls.forEach((url, index) => {
              responseContent += `${index + 1}. [${url}](${url})\n`;
            });

            responseContent += "\n**Please check these sources for information related to your query.**";

            yield { type: "token", content: responseContent };
            yield { type: "end" };
            return;
          }
        } catch (searchError) {
          logger.error(searchError, 'Error during fallback manual search in completion stream');
        }
      }

      yield {
        type: "token",
        content: "## AI Currently Unavailable\n\nThe AI is currently unavailable due to high demand. **Please try again later or contact support.**"
      };
      yield { type: "end" };
      return;
    }

    throw new ProviderError(`Stream iteration error: ${iterationError.message || "Unknown stream error"}`);
  }
}

module.exports = { getOpenAIClient, runAgent };
