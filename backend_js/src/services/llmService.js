const OpenAI = require('openai');
const logger = require('../config/logger');
const { AbortError, default: pRetry } = require('p-retry');
const { searchToolDefinition, executeSearchTool } = require('./tools/searchTool');

let openAIClientInstance = null;

const getOpenAIClient = () => {
  if (!process.env.OPENAI_API_KEY) {
    logger.error(
      'OPENAI_API_KEY is not set in environment variables. LLM service cannot connect.'
    );
    throw new Error('OPENAI_API_KEY is not configured.');
  }

  if (!openAIClientInstance) {
    try {
      let options = {
        apiKey: process.env.OPENAI_API_KEY,
      }
      if(process.env.OPENAI_API_URL){
        options['baseURL'] = process.env.OPENAI_API_URL
      }
      openAIClientInstance = new OpenAI(options);
      logger.info('OpenAI client initialized.');
    } catch (error) {
      logger.error(error, 'Failed to initialize OpenAI client for OpenRouter.');
      throw error;
    }
  }
  return openAIClientInstance;
};

class ProviderError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ProviderError';
  }
}

const DEFAULT_MODEL = process.env.CHAT_LLM_MODEL || 'qwen/qwq-32b:free';

async function* runAgent(
  userPrompt,
  conversationId,
  currentConversationHistory,
) {
  logger.info({ userPrompt, conversationId, historyLength: currentConversationHistory.length }, 'runAgent called');

  const client = getOpenAIClient();
  const tools = [searchToolDefinition];

  const systemMessageContent = `You are Cybro AI, an expert assistant for Cybrosys Technologies, specializing in Odoo, PostgreSQL, and related topics. Your goal is to provide accurate, detailed answers based *exclusively* on information retrieved from the Cybrosys documentation using your search tool.

You must follow this exact process:

1.  **Analyze and Plan:**
    *   Carefully analyze the user's question.
    *   Formulate an optimal, concise search query that is most likely to find the relevant document in the Cybrosys documentation. Do not just use the user's raw question.
    *   *Example:* If the user asks "how do I set up a new company in odoo 17?", a good search query would be "odoo 17 setup new company". If they ask "how old is cybrosys?", a good query is "about cybrosys technologies".

2.  **Execute Search:**
    *   Call the \`search_cybrosys_docs\` tool with your generated query. Start with an offset of 0.

3.  **Evaluate Search Results:**
    *   Critically examine the search results provided.
    *   Ask yourself: "Is this content directly relevant to the user's specific question? Is it for the correct software version if mentioned? Is it on-topic?"

4.  **Decide Next Action (Crucial Step):**
    *   **If the results are highly relevant:** Proceed to step 5.
    *   **If the results are empty, irrelevant, or for the wrong topic/version:** Do NOT attempt to answer. Instead, you MUST try to search again. Formulate a *new, different* search query or use the same query but with an incremented offset (e.g., offset 3, then offset 6). You can make up to 2 re-attempts.
    *   **If after multiple attempts you still have no relevant information:** You must state that you could not find any relevant information in the documentation. Do not apologize excessively.

5.  **Synthesize and Cite:**
    *   Once you have good search results, formulate a comprehensive, detailed answer based *only* on the information from those results. Explain concepts and steps clearly.
    *   At the end of your answer, you MUST provide a 'Sources:' section.
    *   **CRITICAL:** Only cite the URLs of the specific documents from which you directly extracted information to form your answer. Do not list sources that were not used.`

  let messages = [
    { role: "system", content: systemMessageContent },
    ...currentConversationHistory.map(msg => ({
      role: msg.role,
      content: msg.content,
      ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
      ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
    })),
    { role: "user", content: userPrompt },
  ];

  let toolExecuted = false;
  let toolOutput = '';

  for (let i = 0; i < 5; i++) {
    try {
      const response = await client.chat.completions.create({
        model: DEFAULT_MODEL,
        messages: messages,
        tools: tools,
        tool_choice: "auto",
        stream: false,
        temperature: 0.7,
        max_tokens: 1000,
      });

      const responseMessage = response.choices[0].message;
      messages.push(responseMessage);

      if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
        toolExecuted = true;
        for (const toolCall of responseMessage.tool_calls) {
          const functionName = toolCall.function.name;
          const functionArgs = JSON.parse(toolCall.function.arguments);

          if (functionName === "search_cybrosys_docs") {
            yield { type: 'status', content: `Searching for: ${functionArgs.query}...` };
            toolOutput = await executeSearchTool(functionArgs.query, functionArgs.offset, conversationId, userPrompt);

            // Check if the tool returned the specific "no results" string
            if (typeof toolOutput === 'string' && toolOutput.includes("No relevant documents found")) {
              if (i < 2) { // Allow up to 2 retries (0, 1, 2)
                messages.push({
                  role: "tool",
                  tool_call_id: toolCall.id,
                  content: toolOutput,
                });
                messages.push({
                  role: "user",
                  content: `The previous search yielded no relevant documents. Please try searching again with an offset of ${functionArgs.offset + 3}.`
                });
                continue; // Continue to the next iteration of the loop
              } else {
                yield { type: 'token', content: "I couldn't find any relevant information in the Cybrosys documentation for your query even after multiple attempts. Please try rephrasing your question." };
                yield { type: 'end' };
                return; // Terminate agent execution
              }
            }

            // Convert the tool output object/array to a JSON string before adding to messages
            const toolOutputString = JSON.stringify(toolOutput);

            messages.push({
              tool_call_id: toolCall.id,
              role: "tool",
              content: toolOutputString, // Content must be a string
            });
            yield { type: 'status', content: 'Processing search results...' };
          } else {
            toolOutput = `Error: Unknown tool called: ${functionName}`;
            messages.push({
              tool_call_id: toolCall.id,
              role: "tool",
              content: toolOutput,
            });
          }
        }
      } else if (responseMessage.content) {
        yield* generateCompletionStream(responseMessage.content, messages, toolExecuted, toolOutput);
        return;
      }
    } catch (error) {
      logger.error(error, `Error during agent turn ${i + 1}`);
      throw new ProviderError(`Agent error: ${error.message || 'Unknown error'}`);
    }
  }

  yield* generateCompletionStream("I'm having trouble processing this request. Can you please rephrase it?", messages, toolExecuted, toolOutput);
}

async function* generateCompletionStream(
  initialContent,
  fullConversationHistory,
  toolWasExecuted,
  toolResults,
) {
  logger.info(
    { initialContent, historyLength: fullConversationHistory.length, toolWasExecuted, toolResultsLength: toolResults.length },
    'generateCompletionStream called for final response'
  );

  const client = getOpenAIClient();

  let messagesForFinalCompletion = [...fullConversationHistory];

  if (toolWasExecuted && toolResults) {
    messagesForFinalCompletion.push({
      role: "user",
      content: `Based on the search results I provided (Consider the search result as your findings), please give a detailed and comprehensive answer to my original question. Remember to cite the sources at the end. Search results: ${JSON.stringify(toolResults)}`,
    });
  }

  try {
    const stream = await pRetry(() => client.chat.completions.create({
      model: DEFAULT_MODEL,
      messages: messagesForFinalCompletion,
      stream: true,
      temperature: 0.7,
      max_tokens: 2000,
    }), {
      retries: 2,
      minTimeout: 1000,
      factor: 2,
      onFailedAttempt: error => {
        logger.warn(
          `LLM final stream creation attempt ${error.attemptNumber} failed. Retries left: ${error.retriesLeft}. Error: ${error.message}`
        );
        if (error.message?.toLowerCase().includes("context_length_exceeded")) {
          throw error;
        }
        if (!(error instanceof ProviderError)) {
          throw new AbortError(error.message);
        }
      },
    });

    for await (const chunk of stream) {
      if (chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta?.content) {
        yield { type: 'token', content: chunk.choices[0].delta.content };
      }
    }
    yield { type: 'end' };
  } catch (iterationError) {
    logger.error(iterationError, "Error during LLM stream iteration for final response.");
    throw new ProviderError(`Stream iteration error: ${iterationError.message || 'Unknown stream error'}`);
  }
}

module.exports = { getOpenAIClient, runAgent };