const { getPgPool } = require('../db/pgPool');
const logger = require('../config/logger');
const prisma = require('../db/prismaClient');
const { pipeline } = require('@xenova/transformers');

// Use a class to encapsulate the feature extraction pipeline
// and handle lazy loading of the model.
class FeatureExtractionPipeline {
    static task = 'feature-extraction';
    static model = 'nomic-ai/nomic-embed-text-v1.5';
    static instance = null;

    static async getInstance(progress_callback = null) {
        if (this.instance === null) {
            this.instance = pipeline(this.task, this.model, { progress_callback });
        }
        return this.instance;
    }
}

const getSearchResults = async (
  queryText,
  numResults = 10,
  offset = 0
) => {
  logger.info({ queryText, numResults }, 'Executing getSearchResults');

  if (!queryText || queryText.trim() === "") {
    logger.warn('getSearchResults called with empty queryText. Returning empty results.');
    return [];
  }

  const pool = getPgPool();
  let client;

  try {
    client = await pool.connect();
    logger.debug('Connected to pg Pool for search');

    // Generate the embedding for the query
    const extractor = await FeatureExtractionPipeline.getInstance();
    const output = await extractor(`search_query: ${queryText}`, { pooling: 'mean', normalize: true });
    const embedding = `[${output.data.join(',')}]`;
    
    const sqlQuery = `
      SELECT 
        c.chunk_text,
        c.chunk_index,
        d.url,
        c.embedding <=> $1::vector AS cosine_similarity
      FROM rag_chunks c
      JOIN rag_document_pages d ON c.page_id = d.id
      WHERE c.embedding <=> $1::vector < 0.3
      ORDER BY cosine_similarity ASC
      LIMIT $2 OFFSET $3;
    `;
    
    const cosineSimilarityQuery = `
      SELECT 
        c.chunk_text,
        c.chunk_index,
        d.url,
        c.embedding <=> $1::vector AS cosine_similarity
      FROM rag_chunks c
      JOIN rag_document_pages d ON c.page_id = d.id
      WHERE c.embedding <=> $1::vector < 0.3
      ORDER BY cosine_similarity ASC
      LIMIT $2 OFFSET $3;
    `;

    const values = [embedding, numResults, offset];

    logger.debug({ sqlQuery, values: [embedding.slice(0, 5), numResults, offset] }, 'Executing chunk vector search query');
    const { rows } = await client.query(sqlQuery, values);
    const { rows: cosineRows } = await client.query(cosineSimilarityQuery, values);
    console.log(cosineRows);
    logger.info({ queryText, resultsCount: rows.length }, 'Search query executed successfully.');

    if (!rows || rows.length === 0) {
      return [];
    }

    // Map rows to a structured format
    const results = rows.map(row => {
      if (!row.url || !row.chunk_text) {
        logger.warn({ row }, 'Search result row missing url or chunk_text, skipping.');
        return null;
      }
      return {
        url: row.url,
        chunkIndex: row.chunk_index,
        content: row.chunk_text,
      };
    }).filter(r => r !== null);

    return results;

  } catch (error) {
    logger.error(error, `Error in getSearchResults for query: "${queryText}"`);
    return [];
  } finally {
    if (client) {
      client.release();
      logger.debug('pg Client released for search');
    }
  }
};

const searchByUrl = async (url) => {
  logger.info({ url }, 'Executing searchByUrl');

  try {
    const results = await prisma.ragDocumentPage.findMany({
      where: {
        url: {
          contains: url,
        },
      },
      select: {
        url: true,
        title: true,
        content: true,
      },
    });

    logger.info({ url, resultsCount: results.length }, 'Search by URL executed successfully.');
    return results;
  } catch (error) {
    logger.error(error, `Error in searchByUrl for URL: "${url}"`);
    return [];
  }
};

const logSearch = async (data) => {
  const {
    conversationId,
    userQuery,
    generatedSearchQuery,
    searchResultsUrls
  } = data;

  logger.info(
    { conversationId, userQuery, generatedSearchQuery, urlsCount: searchResultsUrls.length },
    'Logging search query'
  );

  try {
    await prisma.searchLog.create({
      data: {
        userQuery: userQuery,
        generatedSearchQuery: generatedSearchQuery,
        searchResultsUrls: searchResultsUrls,
        ...(conversationId && {
          conversation: {
            connect: { id: conversationId }
          }
        })
      },
    });
    logger.info({ conversationId, generatedSearchQuery }, 'Search logged successfully.');
  } catch (error) {
    logger.error(
      error,
      `Failed to log search for conversationId: ${conversationId}, query: "${generatedSearchQuery}"`
    );
  }
};

module.exports = { getSearchResults, logSearch, searchByUrl };
