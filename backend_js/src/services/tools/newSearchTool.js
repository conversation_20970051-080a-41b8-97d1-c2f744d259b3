const { getSearchResults, logSearch, searchByUrl } = require('../newSearchService');
const logger = require('../../config/logger');

const searchToolDefinition = {
  type: "function",
  function: {
    name: "search_cybrosys_docs",
    description:
      "Searches Cybrosys documentation and articles for information. Use this tool to answer questions about Cybrosys products, services, technical details, or any information that might be found in their internal or public documentation. Always use this tool if the user asks a question that requires factual information about Cybrosys that is not already in the conversation history.",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query to find relevant Cybrosys documentation.",
        },
        offset: {
          type: "integer",
          description:
            "The number of results to skip. Useful for pagination or getting more results if the initial ones are not satisfactory.",
          default: 0,
        },
      },
      required: ["query"],
    },
  },
};

const searchByUrlToolDefinition = {
  type: "function",
  function: {
    name: "search_cybrosys_docs_with_url",
    description:
      "Searches Cybrosys full documentation and articles for information. Use this tool to answer questions about Cybrosys products, services, technical details, or any information that might be found in their internal or public documentation. Always use this tool if the user asks a question that requires factual information about Cybrosys that is not already in the conversation history.",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "the url of Cybrosys documentation.",
        },
      },
      required: ["url"],
    },
  },
};

async function executeSearchTool(query, offset = 0, conversationId, userQuery) {
  query = query.trim();

  logger.info({ tool: "search_cybrosys_docs", query, offset }, "Executing search tool");

  try {
    const results = await getSearchResults(query, 5, offset);

    if (results.length > 0) {
      const urlsForLogging = results.map((result) => result.url);

      // Log search in background, no need to await
      logSearch({
        conversationId,
        userQuery,
        generatedSearchQuery: query,
        searchResultsUrls: urlsForLogging,
      }).catch((logError) => {
        logger.error(logError, "Failed to log search in background");
      });

      // Return full results array for LLM to parse and use
      return results;
    }

    // No results found message — keep as string for your agent logic
    return "No relevant documents found for the query.";
  } catch (error) {
    logger.error(error, `Error executing search tool for query: ${query}`);
    return `Error performing search: ${error.message || "Unknown error"}`;
  }
}

async function executeSearchByUrlTool(url) {
  const results = await searchByUrl(url);
  return results;
}

module.exports = { searchToolDefinition, executeSearchTool, executeSearchByUrlTool, searchByUrlToolDefinition };
