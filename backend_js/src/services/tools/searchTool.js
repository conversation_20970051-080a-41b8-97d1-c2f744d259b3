const { getSearchResults, logSearch } = require('../searchService');
const logger = require('../../config/logger');

const searchToolDefinition = {
  type: "function",
  function: {
    name: "search_cybrosys_docs",
    description: "Searches Cybrosys documentation and articles for information. Use this tool to answer questions about Cybrosys products, services, technical details, or any information that might be found in their internal or public documentation. Always use this tool if the user asks a question that requires factual information about Cybrosys that is not already in the conversation history.",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query to find relevant Cybrosys documentation."
        },
        offset: {
          type: "integer",
          description: "The number of results to skip. Useful for pagination or getting more results if the initial ones are not satisfactory.",
          default: 0
        }
      },
      required: ["query"]
    }
  }
};

async function executeSearchTool(query, offset = 0, conversationId, userQuery) {
  logger.info({ tool: "search_cybrosys_docs", query, offset }, "Executing search tool");
  try {
    const results = await getSearchResults(query, 3, offset);
    
    if (results.length > 0) {
      // Extract URLs for logging purposes
      const urlsForLogging = results.map(result => result.url);

      logSearch({
        conversationId: conversationId,
        userQuery: userQuery,
        generatedSearchQuery: query,
        searchResultsUrls: urlsForLogging
      }).catch(logError => {
          logger.error(logError, 'Failed to log search in background');
      });
    }

    if (results.length === 0) {
      return "No relevant documents found for the query.";
    } else {
      // Return the full results object for the LLM to process
      return results;
    }
  } catch (error) {
    logger.error(error, `Error executing search tool for query: ${query}`);
    return `Error performing search: ${error.message || 'Unknown error'}`;
  }
}

module.exports = { searchToolDefinition, executeSearchTool };