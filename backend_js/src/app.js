const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const pinoHttp = require('pino-http');
const logger = require('./config/logger');
const conversationRoutes = require('./routes/conversationRoutes');

const path = require('path');
const chatRoutes = require('./routes/chatRoutes');
const feedbackRoutes = require('./routes/feedbackRoutes');

dotenv.config();

const app = express();

app.use(pinoHttp({ logger }));

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the React app
app.use(express.static(path.join(__dirname, '../../frontend/dist')));

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'UP', timestamp: new Date().toISOString() });
});

app.use('/api/v1/chat', conversationRoutes);
app.use('/api/v1/chat', chatRoutes);
app.use('/api/v1', feedbackRoutes);


// The "catchall" handler: for any request that doesn't
// match one above, send back React's index.html file.
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../../frontend/dist/index.html'));
});

app.use((err, req, res, next) => {
  logger.error({ err, reqDetails: { method: req.method, url: req.originalUrl, ip: req.ip } }, `Unhandled error: ${err.message}`);
  res.status(500).json({ error: 'Internal Server Error', message: err.message });
});

app.use((req, res) => {
    logger.warn({ url: req.originalUrl, method: req.method }, 'Route not found');
    res.status(404).json({ error: 'Not Found', message: `Route ${req.method} ${req.originalUrl} not found.` });
});

module.exports = app;