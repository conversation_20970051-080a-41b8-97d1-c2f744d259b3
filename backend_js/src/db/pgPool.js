const { Pool } = require('pg');
const logger = require('../config/logger');

let pool = null;

const initializePgPool = () => {
  if (pool) {
    return pool;
  }

  if (!process.env.DATABASE_URL) {
    logger.error('DATABASE_URL environment variable is not set. Cannot initialize pg Pool.');
    throw new Error('DATABASE_URL environment variable is not set.');
  }

  try {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });

    pool.on('connect', (client) => {
      logger.info('New client connected to pg Pool');
    });

    pool.on('error', (err, client) => {
      logger.error({ err, pgClient: client?.constructor?.name }, 'Unexpected error on idle pg client');
    });

    logger.info('pg Pool initialized successfully.');
    return pool;

  } catch (error) {
    logger.error(error, 'Failed to initialize pg Pool');
    throw error;
  }
};

const getPgPool = () => {
  if (!pool) {
    return initializePgPool();
  }
  return pool;
};

const disconnectPgPool = async () => {
  if (pool) {
    await pool.end();
    logger.info('pg Pool disconnected.');
    pool = null;
  }
};

module.exports = { getPgPool, disconnectPgPool };