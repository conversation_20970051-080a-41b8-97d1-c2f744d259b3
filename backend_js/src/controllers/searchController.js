const { getSearchResults } = require('../services/newSearchService');
const logger = require('../config/logger');
const { z } = require('zod');

const SearchRequestSchema = z.object({
  text: z.string().min(1, { message: "Search text cannot be empty" }),
  limit: z.number().int().min(1).max(100).optional().default(5),
});

const handleSearchRequest = async (req, res) => {
  const validationResult = SearchRequestSchema.safeParse(req.body);

  if (!validationResult.success) {
    logger.warn({ errors: validationResult.error.flatten().fieldErrors }, 'Search request validation failed');
    return res.status(400).json({
      error: 'Invalid request body',
      details: validationResult.error.flatten().fieldErrors
    });
  }

  const { text, limit } = validationResult.data;

  try {
    logger.info({ text, limit }, 'Processing search request');
    
    const searchResults = await getSearchResults(text, limit, 0);
    
    logger.info({ 
      text, 
      limit, 
      resultsCount: searchResults.length 
    }, 'Search request completed successfully');

    return res.status(200).json({
      success: true,
      query: text,
      limit: limit,
      results: searchResults,
      count: searchResults.length
    });

  } catch (error) {
    logger.error({ error, text, limit }, 'Error processing search request');
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process search request'
    });
  }
};

module.exports = { handleSearchRequest };
