const prisma = require('../db/prismaClient');
const logger = require('../config/logger');
const { sendSseEvent } = require('../utils/sseUtils');
const { runAgent } = require('../services/newLlmService');
const { z } = require('zod');

const ChatRequestSchema = z.object({
  userId: z.string().uuid({ message: "Invalid userId format (must be a UUID)" }),
  message: z.string().min(1, { message: "Message cannot be empty" }),
  conversationId: z.string().uuid({ message: "Invalid conversationId format (must be a UUID)" }).optional().nullable(),
  model: z.string().optional(),
});

const handleChatRequest = async (req, res) => {
  const validationResult = ChatRequestSchema.safeParse(req.body);

  if (!validationResult.success) {
    logger.warn({ errors: validationResult.error.flatten().fieldErrors }, 'Chat request validation failed');
    return res.status(400).json({
      error: 'Invalid request body',
      details: validationResult.error.flatten().fieldErrors
    });
  }

  const {
    userId,
    message: messageContent,
    conversationId: requestConversationId,
  } = validationResult.data;

  logger.info({ validatedBody: validationResult.data }, 'Chat request validated successfully');

  await prisma.userProfile.upsert({
      where: { id: userId },
      update: {},
      create: { id: userId },
  });

  let conversation = requestConversationId
    ? await prisma.conversation.findUnique({ where: { id: requestConversationId, userId: userId } })
    : null;

  let isNewConversation = false;
  if (!conversation) {
    conversation = await prisma.conversation.create({
      data: { userId: userId, title: "New Chat" },
    });
    isNewConversation = true;
  }

  const userMessage = await prisma.message.create({
    data: { conversationId: conversation.id, role: 'user', content: messageContent },
  });

  if (isNewConversation || conversation.title === "New Chat") {
    const messageCount = await prisma.message.count({ where: { conversationId: conversation.id }});
    if (messageCount <=1) {
        const newTitle = messageContent.substring(0, 30) + (messageContent.length > 30 ? "..." : "");
        conversation = await prisma.conversation.update({
            where: { id: conversation.id },
            data: { title: newTitle, updatedAt: new Date() },
        });
    }
  }

  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no');
  res.flushHeaders();
  logger.info({ conversationId: conversation.id }, 'SSE stream headers sent. Connection open.');

  req.on('close', () => {
      logger.info({ conversationId: conversation.id, userId }, 'Client closed SSE connection.');
      res.end();
  });

  let aiMessage = null;
  let fullResponseContent = "";
  let isFirstToken = true;

  try {
    const dbHistory = await prisma.message.findMany({
      where: { conversationId: conversation.id },
      orderBy: { timestamp: 'asc' },
    });

    const agentConversationHistory = dbHistory
      .filter(msg => msg.id !== userMessage.id)
      .map(msg => {
        // Map Prisma messages to the format expected by the agent
        // Only include role and content for now, as tool_calls and tool_call_id are handled by OpenAI's API directly
        return { role: msg.role, content: msg.content };
      });

    for await (const event of runAgent(messageContent, conversation.id, agentConversationHistory)) {
      if (res.writableEnded) {
        logger.warn({ conversationId: conversation?.id }, "Client disconnected during agent processing. Breaking loop.");
        break;
      }

      if (event.type === 'status') {
        sendSseEvent(res, 'status', { content: event.content });
        logger.info({ conversationId: conversation.id, status: event.content }, "Yielded: Status update from agent.");
      } else if (event.type === 'token') {
        if (!aiMessage) {
          aiMessage = await prisma.message.create({
            data: {
              conversationId: conversation.id,
              role: 'assistant',
              content: '',
              modelName: event.modelName || null,
            },
          });
          logger.info({ conversationId: conversation.id, aiMessageId: aiMessage.id }, "Placeholder AI message created by agent.");
        }
        fullResponseContent += event.content;
        sendSseEvent(res, 'token', { content: event.content, isFirst: isFirstToken });
        isFirstToken = false;

        if (fullResponseContent.length % 20 === 0 && fullResponseContent.length > 0) {
          if (!res.writableEnded) {
              await prisma.message.update({
                  where: { id: aiMessage.id },
                  data: { content: fullResponseContent },
              });
          } else {
              logger.info({ conversationId: conversation?.id, aiMessageId: aiMessage?.id }, "Client disconnected, periodic DB update for AI message skipped.");
              break;
          }
        }
      } else if (event.type === 'source') {
        sendSseEvent(res, 'source', { url: event.url, title: event.title, snippet: event.snippet });
        logger.info({ conversationId: conversation.id, source: event.url }, "Yielded: Source from agent.");
      } else if (event.type === 'end') {
        if (aiMessage) {
          await prisma.message.update({
              where: { id: aiMessage.id },
              data: { content: fullResponseContent },
          });
          logger.info({ conversationId: conversation?.id, aiMessageId: aiMessage?.id, finalLength: fullResponseContent.length }, "Final AI message content updated by agent.");
        }

        sendSseEvent(res, 'end', {
            conversationId: conversation.id,
            userMessageId: userMessage.id,
            aiMessageId: aiMessage?.id,
        });
        logger.info({ conversationId: conversation.id }, "Yielded: End event from agent.");
        break;
      }
    }

  } catch (error) {
    logger.error(error, `Core error during SSE streaming for conversation ${conversation?.id}`);
    if (!res.headersSent) {
        res.status(500).json({ error: 'Failed to process chat request', details: (error.message || 'Unknown server error') });
        return;
    }

    if (!res.writableEnded) {
      try {
        sendSseEvent(res, 'error', {
          error: 'An error occurred during the chat.',
          details: (error.message || 'Unknown server error')
        });
        logger.info({ conversationId: conversation?.id }, "Sent SSE error event to client.");
      } catch (sseError) {
        logger.error(sseError, 'Failed to send SSE error event after main stream error.');
      }
    }
  } finally {
    if (!res.writableEnded) {
      res.end();
      logger.info({ conversationId: conversation?.id }, 'SSE stream explicitly ended in controller finally block.');
    }
  }
};

module.exports = { handleChatRequest };