const prisma = require('../db/prismaClient');
const asyncHandler = require('../utils/asyncHandler');

const submitMessageFeedback = asyncHandler(async (req, res) => {
  const { messageId, feedbackType } = req.body;

  if (!messageId || !feedbackType) {
    return res.status(400).json({ error: 'messageId and feedbackType are required' });
  }
  if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(messageId)) {
    return res.status(400).json({ error: 'Invalid messageId format' });
  }
  if (feedbackType !== 'like' && feedbackType !== 'dislike' && feedbackType !== null) {
    return res.status(400).json({ error: 'Invalid feedbackType. Must be "like", "dislike", or null.' });
  }

  const messageExists = await prisma.message.findUnique({
    where: { id: messageId }
  });
  if (!messageExists) {
    return res.status(404).json({ error: 'Message not found' });
  }
  if (messageExists.role !== 'assistant') {
      return res.status(400).json({ error: 'Feedback can only be submitted for assistant messages.'})
  }

  const updatedMessage = await prisma.message.update({
      where: { id: messageId },
      data: { feedback: feedbackType },
  });

  res.status(200).json({ status: 'Feedback updated', message: updatedMessage });
});

module.exports = { submitMessageFeedback };