const prisma = require('../db/prismaClient');
const asyncHandler = require('../utils/asyncHandler');

const getUserConversations = asyncHandler(async (req, res) => {
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).json({ error: 'userId query parameter is required' });
  }
  if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(userId)) {
    return res.status(400).json({ error: 'Invalid userId format' });
  }

  const conversations = await prisma.conversation.findMany({
    where: { userId: userId },
    orderBy: { updatedAt: 'desc' },
    select: {
      id: true,
      title: true,
      updatedAt: true,
      _count: {
        select: { messages: true }
      }
    }
  });

  const responseConversations = conversations.map(conv => ({
    id: conv.id,
    title: conv.title,
    updated_at: conv.updatedAt,
    message_count: conv._count.messages
  }));

  res.status(200).json(responseConversations);
});

const getConversationDetails = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).json({ error: 'userId query parameter is required' });
  }
  if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(userId)) {
    return res.status(400).json({ error: 'Invalid userId format' });
  }
  if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(conversationId)) {
    return res.status(400).json({ error: 'Invalid conversationId format' });
  }

  const conversation = await prisma.conversation.findUnique({
    where: {
      id: conversationId,
      userId: userId
    },
    include: {
      messages: {
        orderBy: { timestamp: 'asc' },
        select: {
          id: true,
          role: true,
          content: true,
          timestamp: true,
          modelName: true,
          feedback: true // Include the feedback field
        }
      }
    }
  });

  if (!conversation) {
    return res.status(404).json({ error: 'Conversation not found or access denied' });
  }

  res.status(200).json(conversation);
});

const deleteUserConversation = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).json({ error: 'userId query parameter is required' });
  }
  if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(userId)) {
    return res.status(400).json({ error: 'Invalid userId format' });
  }
  if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(conversationId)) {
    return res.status(400).json({ error: 'Invalid conversationId format' });
  }

  const conversation = await prisma.conversation.findUnique({
    where: {
      id: conversationId,
      userId: userId,
    },
  });

  if (!conversation) {
    return res.status(404).json({ error: 'Conversation not found or access denied' });
  }

  await prisma.conversation.delete({
    where: {
      id: conversationId,
    },
  });

  res.status(200).json({ status: 'Conversation deleted successfully' });
});

module.exports = {
  getUserConversations,
  getConversationDetails,
  deleteUserConversation,
};