# API Documentation

This document provides detailed information about the API endpoints available in the Cybro AI Chat Node.js backend.

*(This document should be populated with details for each endpoint, including: HTTP Method, URL Path, Description, Request Headers (if any specific), Request Body schema (with examples), Success Response schema (with examples and status codes), Error Response schemas (with examples and status codes). Tools like <PERSON>wa<PERSON> UI or Postman can be used to generate and/or display this information based on an OpenAPI specification.)*

## Base URL

All API endpoints are prefixed with `/api/v1`.

## Authentication

*(Describe authentication mechanism if/when implemented. Currently, `userId` is passed directly, implying trust or external authentication.)*

## Endpoints

### Chat Streaming

*   **POST `/chat/`**
    *   **Description:** Initiates a chat session and streams back responses using Server-Sent Events (SSE).
    *   **Request Body:** `application/json`
        ```json
        {
          "userId": "uuid_string",
          "message": "string",
          "conversationId": "uuid_string_or_null_optional",
          "model": "string_optional"
        }
        ```
    *   **Response:** `text/event-stream`
        *   Streams events like `status`, `token`, `search_preview`, `source`, `end`, `error`. (Details of each event's data structure should be added here).
    *   **Example Usage:** (curl, JavaScript fetch, etc.)

### Conversation History

*   **GET `/chat/history?userId=<uuid>`**
    *   **Description:** Retrieves a list of conversations for a given user.
    *   **Query Parameters:**
        *   `userId` (string, UUID, required): The ID of the user.
    *   **Success Response (200 OK):** `application/json`
        ```json
        [
          {
            "id": "uuid_string",
            "title": "string",
            "updated_at": "datetime_string_iso",
            "message_count": "integer"
          }
        ]
        ```
    *   **Error Responses:** 400 (Invalid userId).

*   **GET `/chat/history/:conversationId?userId=<uuid>`**
    *   **Description:** Retrieves details (including messages) for a specific conversation.
    *   **Path Parameters:**
        *   `conversationId` (string, UUID, required): The ID of the conversation.
    *   **Query Parameters:**
        *   `userId` (string, UUID, required): The ID of the user (for ownership verification).
    *   **Success Response (200 OK):** `application/json` (Structure based on Prisma's `Conversation` model with included `messages`).
    *   **Error Responses:** 400 (Invalid IDs), 404 (Not Found or access denied).

*   **DELETE `/chat/history/:conversationId?userId=<uuid>`**
    *   **Description:** Deletes a specific conversation.
    *   **Path Parameters:**
        *   `conversationId` (string, UUID, required): The ID of the conversation to delete.
    *   **Query Parameters:**
        *   `userId` (string, UUID, required): The ID of the user (for ownership verification).
    *   **Success Response (200 OK):** `application/json`
        ```json
        {
          "status": "Conversation deleted successfully"
        }
        ```
    *   **Error Responses:** 400 (Invalid IDs), 404 (Not Found or access denied).


### Feedback

*   **POST `/feedback/`**
    *   **Description:** Submits feedback (like/dislike) for an assistant's message.
    *   **Request Body:** `application/json`
        ```json
        {
          "userId": "uuid_string",
          "messageId": "uuid_string",
          "feedbackType": "'like' | 'dislike'"
        }
        ```
    *   **Success Response (201 Created / 200 OK):** `application/json`
        ```json
        {
          "status": "Feedback recorded" or "Feedback updated",
          "feedbackId": "integer"
        }
        ```
    *   **Error Responses:** 400 (Invalid input), 404 (User or Message not found).

### Search

*   **POST `/search/`**
    *   **Description:** Performs a semantic search using the provided text query and returns relevant results from the knowledge base.
    *   **Request Body:** `application/json`
        ```json
        {
          "text": "string",
          "limit": "integer_optional_default_5_max_100"
        }
        ```
    *   **Success Response (200 OK):** `application/json`
        ```json
        {
          "success": true,
          "query": "string",
          "limit": "integer",
          "results": [
            {
              "url": "string",
              "chunkIndex": "integer",
              "content": "string"
            }
          ],
          "count": "integer"
        }
        ```
    *   **Error Responses:**
        *   400 (Invalid input - empty text, invalid limit)
        *   500 (Internal server error)
    *   **Example Usage:**
        ```bash
        curl -X POST http://chat.cybrosys.com/api/v1/search \
          -H "Content-Type: application/json" \
          -d '{"text": "What is Odoo?", "limit": 5}'
        ```

### Health Check

*   **GET `/health`** (Note: This is not under `/api/v1`)
    *   **Description:** Returns the health status of the server.
    *   **Success Response (200 OK):** `application/json`
        ```json
        {
          "status": "UP",
          "timestamp": "datetime_string_iso"
        }
        ```
