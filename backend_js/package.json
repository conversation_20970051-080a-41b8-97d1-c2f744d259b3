{"name": "backend_js", "version": "1.0.0", "description": "Node.js backend for Cybro Chat (JavaScript)", "main": "src/server.js", "scripts": {"start": "node src/server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.10.1", "@xenova/transformers": "^2.17.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "openai": "^4.40.0", "p-retry": "^6.2.0", "pg": "^8.11.5", "pino": "^9.1.0", "pino-http": "^10.0.0", "pino-pretty": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"prisma": "^6.10.1"}}