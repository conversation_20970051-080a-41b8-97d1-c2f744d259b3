// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"] 
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  shadowDatabaseUrl      = env("SHADOW_DATABASE_URL")
  extensions = [vector]
}

model UserProfile {
  id            String      @id @default(uuid())
  createdAt     DateTime    @default(now()) @map("created_at")
  conversations Conversation[]

  @@map("user_profiles")
}

model Conversation {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  user      UserProfile @relation(fields: [userId], references: [id], onDelete: Cascade)
  title     String   @default("New Chat")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  messages  Message[]
  searchLogs SearchLog[]

  @@index([userId])
  @@map("conversations")
}

enum Role {
  user
  assistant
  system
}

model Message {
  id             String      @id @default(uuid())
  conversationId String      @map("conversation_id")
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  role           Role
  content        String      @db.Text
  timestamp      DateTime    @default(now())
  modelName      String?     @map("model_name")
  feedback       FeedbackType? // New field for feedback

  @@index([conversationId])
  @@map("messages")
}

enum FeedbackType {
  like
  dislike
}



model SearchLog {
  id                      Int      @id @default(autoincrement())
  conversationId          String?  @map("conversation_id")
  conversation            Conversation? @relation(fields: [conversationId], references: [id], onDelete: SetNull)
  userQuery               String   @db.Text @map("user_query")
  generatedSearchQuery    String   @db.Text @map("generated_search_query")
  searchResultsUrls       Json     @default("[]") @map("search_results_urls") // Prisma uses Json type for JSONField
  timestamp               DateTime @default(now())

  @@index([conversationId])
  @@map("search_logs")
}

model CybroPage {
  id              Int       @id @default(autoincrement())
  url             String    @unique @db.Text
  title           String    @db.Text
  content         String    @db.Text
  keywords        String?   @db.Text
  updatedDate     DateTime? @map("updated_date") @db.Timestamptz
  metaTitle       String?   @map("meta_title") @db.Text
  metaDescription String?   @map("meta_description") @db.Text
  category        String?   @db.Text
  vectors         Unsupported("vector(768)")?

  @@map("cybro_pages")
}

model RagDocumentPage {
  id              Int             @id @default(autoincrement())
  url             String          @unique @db.Text
  title           String          @db.Text
  content         String          @db.Text
  keywords        String?   @db.Text
  updatedDate     DateTime?       @map("updated_date") @db.Timestamptz
  category        String?         @db.Text
  metaTitle       String?         @map("meta_title") @db.Text
  metaDescription String?         @map("meta_description") @db.Text
  createdAt       DateTime        @default(now()) @map("created_at")

  chunks          RagChunk[]      // 1:N relationship to chunks

  @@map("rag_document_pages")
}

model RagChunk {
  id          Int             @id @default(autoincrement())
  pageId      Int             @map("page_id")
  page        RagDocumentPage @relation(fields: [pageId], references: [id], onDelete: Cascade)
  chunkIndex  Int             @map("chunk_index")
  chunkText   String          @db.Text @map("chunk_text")
  embedding   Unsupported("vector(768)")? @map("embedding")
  createdAt   DateTime        @default(now()) @map("created_at")

  @@index([pageId])
  @@unique([pageId, chunkIndex])  // Add this unique constraint
  @@map("rag_chunks")
}
