import React, { useState } from 'react';
import { useChatStore } from '../../stores/chatStore';

interface SidebarProps {
  isVisible: boolean;
  onToggle: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isVisible, onToggle }) => {
  const {
    conversations,
    currentConversationId,
    isLoadingHistory,
    loadConversation,
    startNewChat,
    deleteConversation
  } = useChatStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [isHovering, setIsHovering] = useState(false);

  const handleNewChat = () => {
    startNewChat();
    if (window.innerWidth < 768) {
      onToggle(); // Close sidebar on mobile after selecting
    }
  };

  const handleSelectConversation = (id: string) => {
    loadConversation(id);
    if (window.innerWidth < 768) {
      onToggle(); // Close sidebar on mobile after selecting
    }
  };

  // Group conversations by date
  const groupConversationsByDate = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const oneMonthAgo = new Date(today);
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const todayConversations = [];
    const weekConversations = [];
    const monthConversations = [];
    const olderConversations = [];

    // Filter conversations based on search query
    const filteredConversations = searchQuery.trim()
      ? conversations.filter(conv =>
          conv.title.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : conversations;

    for (const conv of filteredConversations) {
      const convDate = new Date(conv.updated_at);

      if (convDate >= today) {
        todayConversations.push(conv);
      } else if (convDate >= oneWeekAgo) {
        weekConversations.push(conv);
      } else if (convDate >= oneMonthAgo) {
        monthConversations.push(conv);
      } else {
        olderConversations.push(conv);
      }
    }

    return {
      today: todayConversations,
      week: weekConversations,
      month: monthConversations,
      older: olderConversations
    };
  };

  const { today, week, month, older } = groupConversationsByDate();

  const renderConversationGroup = (title: string, conversations: any[]) => {
    if (conversations.length === 0) return null;

    const handleDelete = (id: string, e: React.MouseEvent) => {
      e.stopPropagation();
      if (window.confirm('Are you sure you want to delete this conversation?')) {
        deleteConversation(id);
      }
    }

    return (
      <div className="cy-chat_history">
        <h4>{title}</h4>
        <ul>
          {conversations.map((conversation) => (
            <li key={conversation.id} className="cy-chat_items" onMouseEnter={() => setIsHovering(true)} onMouseLeave={() => setIsHovering(false)}>
              <a
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handleSelectConversation(conversation.id);
                }}
                className={currentConversationId === conversation.id ? 'active' : ''}
              >
                {conversation.title}
              </a>
              {isHovering && (
                <button
                  className="delete-button"
                  onClick={(e) => handleDelete(conversation.id, e)}
                >
                  <i className="ri-delete-bin-line icon-white"></i>
                  {/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  </svg> */}
                </button>
              )}
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`cy-sidebar ${!isVisible ? 'hidden' : ''}`} id="sidebar">
      <div className="cy-sidebarsticky">
        <div className="d-flex align-items-center justify-content-between">
          <div>
            <img src="/assets/new-chat-icon.svg" alt="New Chat" onClick={handleNewChat} style={{cursor: 'pointer'}} />
          </div>
          <button className="btn-sidebar toggle-btn" onClick={onToggle}>
            <img src="/assets/sidebar.svg" alt="Toggle Sidebar" />
          </button>
        </div>
        <div className="cy-search_btn">
          <input
            type="text"
            className="cy-search_input"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div className="cy-search_icon"><i className="ri-search-line"></i></div>
        </div>
      </div>

      {isLoadingHistory ? (
        <div className="cy-chat_history">
          <p>Loading conversations...</p>
        </div>
      ) : conversations.length === 0 ? (
        <div className="cy-chat_history">
          <p>No conversations yet</p>
        </div>
      ) : (
        <>
          {renderConversationGroup('Today', today)}
          {renderConversationGroup('Last 1 Week', week)}
          {renderConversationGroup('Last 1 Month', month)}
          {renderConversationGroup('Older', older)}
        </>
      )}
    </div>
  );
};

export default Sidebar;
