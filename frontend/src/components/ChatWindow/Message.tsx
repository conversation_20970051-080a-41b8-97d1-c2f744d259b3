import React from 'react';
import type { AppMessage, MessageSource } from '../../types/appTypes';
import MarkdownRenderer from '../Common/MarkdownRenderer';
import LoadingSpinner from '../Common/LoadingSpinner';
import Button from '../Common/Button';

interface MessageProps {
  message: AppMessage;
  onFeedback?: (messageId: string, type: 'like' | 'dislike') => void;
  sources?: MessageSource[];
}

const Message: React.FC<MessageProps> = ({ message, onFeedback, sources = [] }) => {
  const { id, type, content, isLoading, feedback } = message;
  
  const isUser = type === 'user';
  const isAssistant = type === 'assistant';
  const isError = type === 'error';
  
  const messageClass = `message ${isUser ? 'user-message' : ''} ${isAssistant ? 'assistant-message' : ''} ${isError ? 'error-message' : ''}`;
  
  const renderFeedbackButtons = () => {
    if (!isAssistant || !onFeedback) return null;
    
    return (
      <div className="message-feedback">
        <Button
          variant="icon"
          onClick={(e) => {
            e.preventDefault(); // Prevent default scroll-to-top behavior
            onFeedback(id, 'like');
          }}
          className={`feedback-button ${feedback === 'like' ? 'active' : ''}`}
          title="Helpful"
        >
          <i className="ri-thumb-up-line"></i>
        </Button>
        <Button
          variant="icon"
          onClick={(e) => {
            e.preventDefault(); // Prevent default scroll-to-top behavior
            onFeedback(id, 'dislike');
          }}
          className={`feedback-button ${feedback === 'dislike' ? 'active' : ''}`}
          title="Not helpful"
        >
          <i className="ri-thumb-down-line"></i>
        </Button>
      </div>
    );
  };
  
  const renderSources = () => {
    if (!sources.length) return null;
    
    return (
      <div className="message-sources">
        <h4 className="sources-title">Sources</h4>
        <ul className="sources-list">
          {sources.map((source, index) => (
            <li key={index} className="source-item">
              <a href={source.url} target="_blank" rel="noopener noreferrer" className="source-link">
                {source.title || source.url}
              </a>
            </li>
          ))}
        </ul>
      </div>
    );
  };
  
  return (
    <div className={messageClass}>
      <div className="message-avatar">
        {isUser ? (
          <div className="user-avatar">
            <i className="ri-user-fill"></i>
          </div>
        ) : (
          <div className="assistant-avatar">
            <i className="ri-robot-fill"></i>
          </div>
        )}
      </div>
      
      <div className="message-content">
        {isLoading ? (
          <div className="message-loading">
            <LoadingSpinner size="small" />
            <span>Generating response...</span>
          </div>
        ) : (
          <>
            <MarkdownRenderer content={content} />
            {renderSources()}
            {renderFeedbackButtons()}
          </>
        )}
      </div>
    </div>
  );
};

export default Message;
