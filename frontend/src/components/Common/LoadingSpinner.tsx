import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  className = '',
}) => {
  const sizeClass = {
    small: 'w-4 h-4',
    medium: 'w-6 h-6',
    large: 'w-8 h-8',
  }[size];
  
  return (
    <div className={`loading-spinner ${sizeClass} ${className}`} aria-label="Loading">
      <div className="spinner-inner"></div>
    </div>
  );
};

export default LoadingSpinner;
