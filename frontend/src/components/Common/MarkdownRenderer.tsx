import React, { useEffect, useRef } from 'react';
import React<PERSON><PERSON>down from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import rehypeSanitize from 'rehype-sanitize';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Add copy buttons to code blocks after rendering
    if (containerRef.current) {
      const codeBlocks = containerRef.current.querySelectorAll('pre');
      
      codeBlocks.forEach((pre) => {
        // Skip if already has a copy button
        if (pre.querySelector('.copy-code-button')) return;
        
        // Create copy button
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-code-button';
        copyButton.innerHTML = '<i class="ri-clipboard-line"></i>';
        copyButton.title = 'Copy to clipboard';
        
        // Add click event
        copyButton.addEventListener('click', () => {
          const code = pre.querySelector('code')?.textContent || '';
          navigator.clipboard?.writeText(code)
            .then(() => {
              copyButton.innerHTML = '<i class="ri-check-line"></i>';
              setTimeout(() => {
                copyButton.innerHTML = '<i class="ri-clipboard-line"></i>';
              }, 2000);
            })
            .catch((err) => {
              console.error('Failed to copy code:', err);
              copyButton.innerHTML = '<i class="ri-close-line"></i>';
              setTimeout(() => {
                copyButton.innerHTML = '<i class="ri-clipboard-line"></i>';
              }, 2000);
            });
        });
        
        // Add button to pre element
        pre.style.position = 'relative';
        pre.appendChild(copyButton);
      });
    }
  }, [content]);
  
  return (
    <div ref={containerRef} className={`markdown-content ${className}`}>
      <ReactMarkdown
        rehypePlugins={[rehypeSanitize]}
        components={{
          code({ node, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            return match ? (
              <SyntaxHighlighter
                style={vscDarkPlus as any}
                language={match[1]}
                PreTag="div"
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          table({ node, ...props }) {
            return (
              <div className="table-container">
                <table {...props} />
              </div>
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
