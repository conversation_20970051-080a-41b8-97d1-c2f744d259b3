import { useEffect, useState } from 'react';
import Sidebar from './components/Sidebar/Sidebar';
import ChatWindow from './components/ChatWindow/ChatWindow';
import { useChatStore } from './stores/chatStore';
import { configureMarked } from './utils/markdownUtils';
import './styles.css';

function App() {
  const { initializeUser } = useChatStore();
  const [sidebarVisible, setSidebarVisible] = useState(false);

  useEffect(() => {
    // Initialize user and load history
    initializeUser();

    // Configure marked for markdown rendering
    configureMarked();

    // Handle window resize
    const handleResize = () => {
      setSidebarVisible(window.innerWidth > 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [initializeUser]);

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <div className="cy-ai_chat">
      {/* Sidebar */}
      <Sidebar isVisible={sidebarVisible} onToggle={toggleSidebar} />

      {/* Chat Content */}
      <div className={`cy-modal_content ${!sidebarVisible ? 'full-width' : ''}`}>
        <button
          className={`cy-sidebar_toggle-btn toggle-btn ${!sidebarVisible ? 'visible' : ''}`}
          onClick={toggleSidebar}
        >
          <img src="/assets/sidebar.svg" alt="Toggle Sidebar" />
        </button>

        <ChatWindow />
      </div>
    </div>
  );
}

export default App
