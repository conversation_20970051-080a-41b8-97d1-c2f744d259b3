import type { ApiMessage, ConversationSummary } from './apiTypes';

export interface AppMessage extends ApiMessage {
  type: 'user' | 'assistant' | 'system' | 'error';
  isLoading?: boolean;
  sources?: MessageSource[];
  feedback?: 'like' | 'dislike' | null;
}

export interface MessageSource {
  url: string;
  title: string;
  snippet: string;
}

export interface ChatState {
  userId: string | null;
  conversations: ConversationSummary[];
  currentConversationId: string | null;
  messages: AppMessage[];
  isLoadingHistory: boolean;
  isLoadingMessages: boolean;
  isSendingMessage: boolean;
  currentStatus: string | null;
  aiTyping: boolean;
  searchResults: MessageSource[];
  
  // Actions
  initializeUser: () => void;
  loadHistory: () => Promise<void>;
  loadConversation: (conversationId: string) => Promise<void>;
  startNewChat: () => void;
  sendMessage: (text: string, model?: string) => Promise<void>;
  addTokenToLastMessage: (token: string) => void;
  setAiMessageComplete: (aiMessageId: string) => void;
  submitFeedback: (messageId: string, feedbackType: 'like' | 'dislike' | null) => Promise<void>;
  setMessageFeedback: (messageId: string, feedbackType: 'like' | 'dislike' | null) => void;
  deleteConversation: (conversationId: string) => Promise<void>;
}
