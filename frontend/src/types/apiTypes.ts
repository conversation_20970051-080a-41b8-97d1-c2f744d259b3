// API Response Types
export interface Provider {
  id: string;
  name: string;
  models: Model[];
}

export interface Model {
  id: string;
  name: string;
}

export interface ConfigResponse {
  providers: Provider[];
}

export interface ConversationSummary {
  id: string;
  title: string;
  updated_at: string;
  message_count: number;
}

export interface ApiMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  model_name?: string;
  feedback?: 'like' | 'dislike' | null;
}

export interface ConversationDetail {
  id: string;
  title: string;
  created_at: string;
  messages: ApiMessage[];
}

// API Request Types
export interface ChatRequest {
  message: string;
  userId: string;
  conversationId?: string | null;
  model?: string;
}



// SSE Event Types
export interface StatusEvent {
  content: string;
}

export interface TokenEvent {
  content: string;
  isFirst?: boolean;
}

export interface SearchPreviewEvent {
  url: string;
  title: string;
}

export interface SourceEvent {
  url: string;
  title: string;
  snippet: string;
}

export interface EndEvent {
  conversationId: string;
  userMessageId: string;
  aiMessageId: string;
}
