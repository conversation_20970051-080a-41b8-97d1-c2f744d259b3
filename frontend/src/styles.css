@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');


* {
    padding: 0px;
    margin: 0px;
    box-sizing: border-box;
    font-family: "Manrope", serif;
}

body {
    background: #fff;
    position: relative;

}

.cy-modal_content {
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    position: relative;
    width: 100%;
    flex-grow: 1;
}

.cy-modal_body {
    text-align: center;
    padding: 4rem 2rem;
}

.cy-modal_dialog {
    max-width: 934px;
}

.cy-modal-header {
    /* color: rgba(158, 167, 0, 1); */
    font-size: 42px;
    font-family: "Plus Jakarta Sans", sans-serif;
    background: var(--gradient, linear-gradient(86deg, #B22126 0.19%, #4C0E10 99.81%));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    font-weight: 700;
    letter-spacing: 0.0165em;
}

.cy-modal_body p {
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: 400;

}

.cy-ai_search-container {
  position: relative;
  display: flex;
  align-items: flex-end; /* aligns textarea bottom */
  padding-right: 50px; /* space for send icon */
  margin-top: 80px;
  margin-bottom: 20px;
  border: 1px solid #CBD5E1;
  border-radius: 50px;
  box-shadow: 0px 4px 8px -2px rgba(23, 23, 23, 0.10), 0px 2px 4px -2px rgba(23, 23, 23, 0.06);
    padding: 1rem 1rem 1rem 1rem;
    font-size: 14px;
  /* max-height: 200px; */
}

.cy-ai_search-container textarea {
  flex: 1;
  font-size: 1rem;
  line-height: 1.5;
  overflow-y: auto;
  transition: all 0.2s ease;
  border: none;
  padding-right: 43px;
  max-height: 90px;
}

/* Custom Scrollbar */
.cy-ai_search-container textarea::-webkit-scrollbar {
  width: 6px;
}
.cy-ai_search-container textarea::-webkit-scrollbar-thumb {
  background: #aaa;
  border-radius: 10px;
}

/* Send Button */
.cy-ai_send-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #000;
  border-radius: 28px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.cy-ai_send-icon:hover {
  opacity: 1;
}


.cy-ai_search--icon img {
    width: 26px;
}



.cy-ai_search-container textarea:focus,:focus-visible {
    outline-color: transparent;
    border: none;

}


.cy-ai_option-list {
    font-size: 14px;
    font-weight: 400;
    line-height: 16.39px;
    letter-spacing: 0.0162em;
    border: 1px solid rgba(68, 70, 39, 0.3);
    /* border-image-source: linear-gradient(270deg, rgba(68, 70, 39, 0.3) 0%, rgba(158, 167, 0, 0.3) 100%); */
    border-image-slice: 1;
    padding: 9px 14px;
    border-radius: 10px !important;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    color: inherit;
    text-decoration: none;
    opacity: 80%;
    transition: all 0.3s linear;
    cursor: pointer;
}

.cy-ai_option-list:hover {
    opacity: 100%;
    color: inherit;
    background: rgba(180, 180, 180, 0.15);

}

.cy-ai_clsbtn {
    position: absolute;
    opacity: 1;
    width: 2em;
    height: 2em;
    right: 24px;
    top: 24px;
    background: url('/assets/close-outline.svg');
    background-repeat: no-repeat;
}

.cy-ai_clsbtn:focus {
    box-shadow: none;
}


.cy-sidebar_toggle-btn {
    position: fixed;
    left: 0px;
    top: 0px;
    width: 36px;
    height: 36px;
    margin: 1rem;
    background-color: #891A1A;
    border-radius: 50px;
    border: none;
    transition: all 0.1s;
    opacity: 0;
    z-index: 111;
}

.cy-ai_chat {
    display: flex;
}


.cy-sidebar {
    width: 300px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    background: #891A1A;
    color: white;
    transition: transform 0.3s ease;
    z-index: 1050;
    padding: 24px;
    overflow-y: auto;
}

.cy-sidebar.hidden {
    transform: translateX(-100%);
}

.cy-modal_content {
    margin-left: 300px;
    transition: margin-left 0.3s ease;
    width: calc(100% - 300px);


}

.cy-modal_content.full-width {
    margin-left: 0;
    width: 100%;
}



.cy-sidebar_toggle-btn.visible {
    opacity: 1;
}

.btn-sidebar {
    background-color: transparent;
    border: none;
    border-radius: 50px;
    width: 36px;
    height: 36px;
}

.btn-sidebar:hover {
    background-color: rgba(245, 246, 230, 0.1);
}

.btn-sidebar img {
    width: 22px;
}

.cy-newchat_btn {
    border: 1px solid #4C0E10;
    background: #4A1111;
    margin: 1.8rem 0;
    width: 100%;
    color: #fff;
    border-radius: 10px;
    padding: 10px;
    font-size: 16px;
    transition: all 0.3s linear;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-weight: 500;
    justify-content: center;

}

.cy-search_btn{
    border: 1px solid #4C0E10;
    background: #4A1111;
    margin: 1.8rem 0;
    width: 100%;
    color: #fff;
    border-radius: 10px;
    padding: 10px;
    font-size: 16px;
    transition: all 0.3s linear;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-weight: 500;
    justify-content: space-between;
}

.cy-search_btn .cy-search_input{
    background-color: transparent;
    border: none;
    box-shadow: none;
    color: #fff;
    color: #DEDBDB;
    font-family: "Plus Jakarta Sans";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
}

.cy-search_btn .cy-search_input:focus{
    outline: none;
    box-shadow: none;
    border: none;
    width: 100%;

}

.cy-search_icon{
    i{
        font-size: 20px;
    }
}


.cy-chat_history ul {
    list-style: none;
    padding-left: 0px;
    padding-bottom: 0.7rem;


}

.cy-chat_history h4 {
    text-transform: uppercase;

    color: #FFF;
font-family: "Plus Jakarta Sans";
font-size: 14px;
font-weight: 700;
line-height: 22px; /* 157.143% */
letter-spacing: -0.098px;
}

.cy-chat_items a {
    font-size: 13px;
    padding: 8px 8px 8px 1.8rem;
    color: #fff;
    max-width: 250px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
    text-decoration: none;
    font-weight: 300;
    display: inline-block;
    align-items: center;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-family: "Plus Jakarta Sans", sans-serif;
    position: relative;

}

.cy-chat_items a:hover {
    color: #fff;
    background-color: rgba(245, 246, 230, 0.1);
}

.cy-chat_items a:after {
    content: '';
    position: absolute;
    left: 0px;
    background-image: url('assets/chat-icon.svg');
    width: 14px;
    height: 14px;
    background-repeat: no-repeat;
    margin-left: 6px;
    bottom: 9px;
}

.cy-sidebarsticky {
    position: sticky;
    top: 0px;
    z-index: 111;
    /* background: linear-gradient(86deg, #4C0E10 0.19%, #B22126 48.53%); */

}

.cy-chat_container {
    height: 100%;
    position: relative;
    height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 50rem;
    margin: 0 auto;
    width: 100%;
    align-items: center;
    justify-content: center;
}

.cy-chat-greeting{
    padding-bottom: 24px;
}

.cy-chat-greeting p{
    text-align: center;
    font-weight: 500;
}



.cy-ai-message_input{
    position: sticky;
    bottom: 0px;
     background-color: #fff;
     width: 100%;
}


.cy-chat_container .cy-ai_search-container {


    margin-top: 0px;
    width: 100%;
    padding: 1rem;
}

.cy-ai_search-container textarea {
    width: 100%;
}

.cy-chating--content {
    width: 100%;
    overflow-y: auto;
    padding: 1rem 0;
    flex: 1;
    padding-bottom: 100px;

}

.cy-chating--content::-webkit-scrollbar {
    width: 0px;
    background: transparent; /* Optional */
  }

.cy-chat_list {
    list-style: none;
    padding: 0px;
    margin-top: 2rem;
}

.cy-chat_list li {
    clear: both;
}

.msg-list {
    margin-bottom: 58px;
    position: relative;
    padding: 20px ;
    font-size: 14px;
    border-radius: 16px;
      min-width: 120px;
    max-width: 82%;
}

.msg-list span{
    word-break: break-all;
}

.msg-option{
    display: flex;
    align-items: center;
    position: absolute;
    font-size: 12px;
    gap: 6px;
    bottom: -32px;
    left: 6px;
    color: rgba(108, 114, 117, 0.5);
}
.chat-prompt .msg-list {
    float: right;
    margin-right: 70px;
    border: 2px solid #F3F5F7;
    border-radius: 20px 20px 0px 20px;
    background: linear-gradient(86deg, #B22126 0.19%, #4C0E10 99.81%);
    color: #fff;
    font-weight: 500;
}

.msg-loading{
    /* background-color: #fff; */
    color: #888;
    border-radius: 20px;
    width:fit-content;
    margin-bottom: 4px;

}

.msg-edit_option{
    color: #222222;
    padding: 4px 8px;
    background-color: #fff;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    font-weight: 600;
    opacity: 80%;
    display: flex;
    gap: 2px ;
    align-items: center;
}
.msg-edit_option:hover{
    opacity: 1;
}

/* Code block styling */
.chat-reply pre {
    background-color: #1e1e1e;
    border-radius: 8px;
    padding: 16px;
    margin: 10px 0;
    position: relative;
    overflow: auto;
}

.chat-reply pre code {
    font-family: 'Courier New', Courier, monospace;
    color: #f8f8f8;
    font-size: 14px;
    
}

.chat-reply p code {
    background-color: #2d2d2d;
    padding: 2px 4px;
    border-radius: 4px;
    color: #f8f8f8;
}

.copy-code-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #333;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.copy-code-button:hover {
    opacity: 1;
}

.msg-option span i {
    font-size: 16px; /* Increased font size */
    padding: 5px; /* Added padding for a larger click area */
    border-radius: 50%;
    transition: all 0.3s ease;
}

.msg-option span i:hover {
    background-color: #B22126;
    color: #fff;
}

.chat-reply .msg-option{
    left: auto;
    right: 0px;
    bottom: -30px;
}
.chat-reply .msg-list {
    float: left;
    margin-left: 70px;
    background-color: #F5F5F5;

}

.chat-reply .msg-list::before {
    position: absolute;
    top: 0;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    overflow: hidden;
    content: "";
    left: -70px;
    background-image: url(assets/ai-chatprompt.svg);
    border: 3px solid #fff;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
}


.cy-field_icons{
    color: #B22126;
    margin-right: 0.3rem;
}

.cy-chat_list ul {
    clear: both;
}

.cy-field_container {
    background-color: #fff;
    border: 1px solid #DFDFDF;
    padding: 24px;
    border-radius: 6px;
    margin-top: 16px;
}

.cy-chat_fields-content {
    margin-bottom: 0.5rem;
    display: flex;

}

.cy-chat_fields-content strong {
    min-width: 180px;
}

.cy-fields-list_item {
    background-color: #fff;
    border: 1px solid #dfdfdf;
    padding: 14px;
    border-radius: 4px;
    font-size: 12.8px;
    list-style: none;
    color: #777777;
    margin-bottom: 0.6rem;
}

.cy-field_name {
    color: #222222;
    font-weight: 600;
    margin-right: 0.2rem;
}

.cy-confirm-btn {
    width: fit-content;
    margin: 1rem 0 0 auto;
    font-size: 12.8px;
    padding: 8px 12px;
}


@media (max-width:1200px) {
    .cy-chat_container {
        max-width: 90%;
    }
}

@media (max-width:768px) {
    .cy-modal_content {
        margin-left: 0;
        width: 100%;
    }


}

@media (max-width:600px) {
    .cy-ai_search-container textarea {
        width: 100%;
        min-width: 300px;

    }


    .cy-modal_body {
        height: auto;
    }

    .cy-ai_search-container {
        margin-top: 2rem;
    }

    .cy-modal-header {

        font-size: 23px;
    }

    .cy-modal_body p {
        font-size: 12px;
    }

    .cy-ai_option-list {
        font-size: 12px;
    }
}

::-webkit-scrollbar {
    width: 4px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #dcdcdc;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: #bbbbbb;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(0, 0%, 33%);
  }

  /* ai-chat-loader*/

  .oh-chat__typing-animation {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    position: relative;
    display: inline-block;
    background-color: rgba(94, 94, 94, 0.4);
    animation: typingAnimation 0.8s ease-in-out 1s infinite;
    top: 3px;
    left: 8px;
    margin-right: 1.5rem;
    transition: all 0.3s ease-in-out;
  }
  .oh-chat__typing-animation::after, .oh-chat__typing-animation::before {
    content: "";
    position: absolute;
    width: inherit;
    height: inherit;
    border-radius: inherit;
    background-color: inherit;
    display: inherit;
    transition: inherit;
    animation: typingAnimation 0.8s ease-in-out infinite;

  }
  .oh-chat__typing-animation::before {
    left: -8px;
    animation: typingAnimation 0.8s ease-in-out infinite;
  }
  .oh-chat__typing-animation::after {
    right: -8px;
    animation: typingAnimation 0.8s ease-in-out 1s infinite;


  }


  @keyframes typingAnimation {
    0% {
      top: 1px;
      background-color: rgba(94, 94, 94, 0.4);
    }
    50% {
      top: -1px;
      background-color: rgba(94, 94, 94, 0.6);
    }
    100% {
      top: 1px;
      background-color: rgba(94, 94, 94, 0.4);
    }
  }


.loading-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
}

.loading-spinner {
  width: 10px;
  height: 10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


@keyframes spinner-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-container i.fa-spinner {
  display: inline-block;
  animation: spinner-rotate 1s linear infinite;
}


  /* ai-chat-loader*/

  .oh-chat__typing-animation {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    position: relative;
    display: inline-block;
    background-color: rgba(94, 94, 94, 0.4);
    animation: typingAnimation 0.8s ease-in-out 1s infinite;
    top: 3px;
    left: 8px;
    margin-right: 1.5rem;
    transition: all 0.3s ease-in-out;
  }
  .oh-chat__typing-animation::after, .oh-chat__typing-animation::before {
    content: "";
    position: absolute;
    width: inherit;
    height: inherit;
    border-radius: inherit;
    background-color: inherit;
    display: inherit;
    transition: inherit;
    animation: typingAnimation 0.8s ease-in-out infinite;

  }
  .oh-chat__typing-animation::before {
    left: -8px;
    animation: typingAnimation 0.8s ease-in-out infinite;
  }
  .oh-chat__typing-animation::after {
    right: -8px;
    animation: typingAnimation 0.8s ease-in-out 1s infinite;


  }


  @keyframes typingAnimation {
    0% {
      top: 1px;
      background-color: rgba(94, 94, 94, 0.4);
    }
    50% {
      top: -1px;
      background-color: rgba(94, 94, 94, 0.6);
    }
    100% {
      top: 1px;
      background-color: rgba(94, 94, 94, 0.4);
    }
  }

  .delete-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, color 0.2s;
  }
  
  .delete-button:hover {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff0000;
  }

  .cy-chat_items {
    display: flex;
  }

  .icon-white {
    color: white;
  }

  .msg-list p {
    word-break: break-word;
  }

  .msg-option .ri-thumb-up-line.active,
.msg-option .ri-thumb-down-line.active {
  color: #891A1A !important; /* Highlight color */
}