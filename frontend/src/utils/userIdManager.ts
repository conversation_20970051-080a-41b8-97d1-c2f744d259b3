import { v4 as uuidv4 } from 'uuid';

const USER_ID_KEY = 'cybroUserId';

/**
 * Gets the user ID from localStorage or creates a new one if it doesn't exist
 * @returns The user ID
 */
export const getUserId = (): string => {
  let userId = localStorage.getItem(USER_ID_KEY);
  
  if (!userId) {
    userId = uuidv4();
    localStorage.setItem(USER_ID_KEY, userId);
  }
  
  return userId;
};

/**
 * Sets a specific user ID in localStorage
 * @param userId The user ID to set
 */
export const setUserId = (userId: string): void => {
  localStorage.setItem(USER_ID_KEY, userId);
};

/**
 * Clears the user ID from localStorage
 */
export const clearUserId = (): void => {
  localStorage.removeItem(USER_ID_KEY);
};
