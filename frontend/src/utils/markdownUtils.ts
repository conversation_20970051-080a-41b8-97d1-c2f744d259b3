import { marked } from 'marked';
import { gfmHeadingId } from "marked-gfm-heading-id";

marked.use(gfmHeadingId());

/**
 * Configures the marked library with appropriate options
 */
export const configureMarked = (): void => {
  marked.use({ gfm: true, breaks: true });
};

/**
 * Processes markdown text to HTML
 * @param text The markdown text to process
 * @returns The processed HTML
 */
export const processMarkdown = async (text: string): Promise<string> => {
  try {
    return await marked.parse(text);
  } catch (e) {
    console.error('Markdown parsing error:', e);
    return text;
  }
};

/**
 * Adds a copy button to all code blocks in the document
 * @param containerSelector The selector for the container element
 */
export const addCopyButtonsToCodeBlocks = (containerSelector: string): void => {
  const container = document.querySelector(containerSelector);
  if (!container) return;
  
  const codeBlocks = container.querySelectorAll('pre code');
  
  codeBlocks.forEach((codeBlock) => {
    const pre = codeBlock.parentElement;
    if (!pre) return;
    
    // Create copy button
    const copyButton = document.createElement('button');
    copyButton.className = 'copy-code-button';
    copyButton.innerHTML = '<i class="ri-clipboard-line"></i>';
    copyButton.title = 'Copy to clipboard';
    
    // Add click event
    copyButton.addEventListener('click', () => {
      const code = codeBlock.textContent || '';
      navigator.clipboard?.writeText(code)
        .then(() => {
          copyButton.innerHTML = '<i class="ri-check-line"></i>';
          setTimeout(() => {
            copyButton.innerHTML = '<i class="ri-clipboard-line"></i>';
          }, 2000);
        })
        .catch((err) => {
          console.error('Failed to copy code:', err);
          copyButton.innerHTML = '<i class="ri-close-line"></i>';
          setTimeout(() => {
            copyButton.innerHTML = '<i class="ri-clipboard-line"></i>';
          }, 2000);
        });
    });
    
    // Add button to pre element
    pre.style.position = 'relative';
    pre.appendChild(copyButton);
  });
};
