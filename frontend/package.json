{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "marked": "^16.0.0", "marked-gfm-heading-id": "^4.1.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "rehype-sanitize": "^6.0.0", "uuid": "^9.0.1", "zustand": "^4.4.6"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.10", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}